{"version": 3, "sources": [], "sections": [{"offset": {"line": 31, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/auth/protected-route.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useAuth } from \"@/contexts/auth-context\";\nimport { useRouter } from \"next/navigation\";\nimport { useEffect } from \"react\";\nimport { Loader2 } from \"lucide-react\";\n\ninterface ProtectedRouteProps {\n  children: React.ReactNode;\n  requiredPermission?: string;\n  fallback?: React.ReactNode;\n}\n\nexport function ProtectedRoute({ \n  children, \n  requiredPermission, \n  fallback \n}: ProtectedRouteProps) {\n  const { isAuthenticated, isLoading, hasPermission } = useAuth();\n  const router = useRouter();\n\n  useEffect(() => {\n    if (!isLoading && !isAuthenticated) {\n      router.push(\"/login\");\n    }\n  }, [isAuthenticated, isLoading, router]);\n\n  if (isLoading) {\n    return (\n      <div className=\"flex items-center justify-center min-h-screen\">\n        <div className=\"flex flex-col items-center space-y-4\">\n          <Loader2 className=\"h-8 w-8 animate-spin\" />\n          <p className=\"text-sm text-muted-foreground\">Loading...</p>\n        </div>\n      </div>\n    );\n  }\n\n  if (!isAuthenticated) {\n    return null; // Will redirect to login\n  }\n\n  if (requiredPermission && !hasPermission(requiredPermission)) {\n    return (\n      fallback || (\n        <div className=\"flex items-center justify-center min-h-screen\">\n          <div className=\"text-center\">\n            <h2 className=\"text-2xl font-bold mb-2\">Access Denied</h2>\n            <p className=\"text-muted-foreground\">\n              You don't have permission to access this page.\n            </p>\n          </div>\n        </div>\n      )\n    );\n  }\n\n  return <>{children}</>;\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AALA;;;;;;AAaO,SAAS,eAAe,EAC7B,QAAQ,EACR,kBAAkB,EAClB,QAAQ,EACY;IACpB,MAAM,EAAE,eAAe,EAAE,SAAS,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAC5D,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IAEvB,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,IAAI,CAAC,aAAa,CAAC,iBAAiB;YAClC,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAiB;QAAW;KAAO;IAEvC,IAAI,WAAW;QACb,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,iNAAA,CAAA,UAAO;wBAAC,WAAU;;;;;;kCACnB,8OAAC;wBAAE,WAAU;kCAAgC;;;;;;;;;;;;;;;;;IAIrD;IAEA,IAAI,CAAC,iBAAiB;QACpB,OAAO,MAAM,yBAAyB;IACxC;IAEA,IAAI,sBAAsB,CAAC,cAAc,qBAAqB;QAC5D,OACE,0BACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAAwB;;;;;;;;;;;;;;;;;IAO/C;IAEA,qBAAO;kBAAG;;AACZ", "debugId": null}}, {"offset": {"line": 137, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/ui/sheet.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SheetPrimitive from \"@radix-ui/react-dialog\"\nimport { XIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Sheet({ ...props }: React.ComponentProps<typeof SheetPrimitive.Root>) {\n  return <SheetPrimitive.Root data-slot=\"sheet\" {...props} />\n}\n\nfunction SheetTrigger({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Trigger>) {\n  return <SheetPrimitive.Trigger data-slot=\"sheet-trigger\" {...props} />\n}\n\nfunction SheetClose({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Close>) {\n  return <SheetPrimitive.Close data-slot=\"sheet-close\" {...props} />\n}\n\nfunction SheetPortal({\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Portal>) {\n  return <SheetPrimitive.Portal data-slot=\"sheet-portal\" {...props} />\n}\n\nfunction SheetOverlay({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Overlay>) {\n  return (\n    <SheetPrimitive.Overlay\n      data-slot=\"sheet-overlay\"\n      className={cn(\n        \"data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction SheetContent({\n  className,\n  children,\n  side = \"right\",\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Content> & {\n  side?: \"top\" | \"right\" | \"bottom\" | \"left\"\n}) {\n  return (\n    <SheetPortal>\n      <SheetOverlay />\n      <SheetPrimitive.Content\n        data-slot=\"sheet-content\"\n        className={cn(\n          \"bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500\",\n          side === \"right\" &&\n            \"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm\",\n          side === \"left\" &&\n            \"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm\",\n          side === \"top\" &&\n            \"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b\",\n          side === \"bottom\" &&\n            \"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <SheetPrimitive.Close className=\"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none\">\n          <XIcon className=\"size-4\" />\n          <span className=\"sr-only\">Close</span>\n        </SheetPrimitive.Close>\n      </SheetPrimitive.Content>\n    </SheetPortal>\n  )\n}\n\nfunction SheetHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-header\"\n      className={cn(\"flex flex-col gap-1.5 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"sheet-footer\"\n      className={cn(\"mt-auto flex flex-col gap-2 p-4\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetTitle({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Title>) {\n  return (\n    <SheetPrimitive.Title\n      data-slot=\"sheet-title\"\n      className={cn(\"text-foreground font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SheetDescription({\n  className,\n  ...props\n}: React.ComponentProps<typeof SheetPrimitive.Description>) {\n  return (\n    <SheetPrimitive.Description\n      data-slot=\"sheet-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Sheet,\n  SheetTrigger,\n  SheetClose,\n  SheetContent,\n  SheetHeader,\n  SheetFooter,\n  SheetTitle,\n  SheetDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAGA;AACA;AAEA;AANA;;;;;AAQA,SAAS,MAAM,EAAE,GAAG,OAAyD;IAC3E,qBAAO,8OAAC,kKAAA,CAAA,OAAmB;QAAC,aAAU;QAAS,GAAG,KAAK;;;;;;AACzD;AAEA,SAAS,aAAa,EACpB,GAAG,OACiD;IACpD,qBAAO,8OAAC,kKAAA,CAAA,UAAsB;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACpE;AAEA,SAAS,WAAW,EAClB,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,QAAoB;QAAC,aAAU;QAAe,GAAG,KAAK;;;;;;AAChE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,SAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OACiD;IACpD,qBACE,8OAAC,kKAAA,CAAA,UAAsB;QACrB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,QAAQ,EACR,OAAO,OAAO,EACd,GAAG,OAGJ;IACC,qBACE,8OAAC;;0BACC,8OAAC;;;;;0BACD,8OAAC,kKAAA,CAAA,UAAsB;gBACrB,aAAU;gBACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8MACA,SAAS,WACP,oIACF,SAAS,UACP,iIACF,SAAS,SACP,4GACF,SAAS,YACP,qHACF;gBAED,GAAG,KAAK;;oBAER;kCACD,8OAAC,kKAAA,CAAA,QAAoB;wBAAC,WAAU;;0CAC9B,8OAAC,gMAAA,CAAA,QAAK;gCAAC,WAAU;;;;;;0CACjB,8OAAC;gCAAK,WAAU;0CAAU;;;;;;;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mCAAmC;QAChD,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,QAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,GAAG,OACqD;IACxD,qBACE,8OAAC,kKAAA,CAAA,cAA0B;QACzB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 309, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 355, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/layout/sidebar.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useAuth } from \"@/contexts/auth-context\";\nimport { Button } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { cn } from \"@/lib/utils\";\nimport {\n  LayoutDashboard,\n  Users,\n  Package,\n  Bell,\n  Settings,\n  Key,\n  Activity,\n  UserCog,\n  LogOut,\n  ChevronDown,\n  ChevronRight,\n} from \"lucide-react\";\nimport Link from \"next/link\";\nimport { usePathname } from \"next/navigation\";\nimport { useState } from \"react\";\n\ninterface SidebarProps {\n  onNavigate?: () => void;\n}\n\ninterface NavItem {\n  title: string;\n  href?: string;\n  icon: React.ComponentType<{ className?: string }>;\n  badge?: string | number;\n  children?: NavItem[];\n  permission?: string;\n}\n\nexport function Sidebar({ onNavigate }: SidebarProps) {\n  const { admin, logout, hasPermission } = useAuth();\n  const pathname = usePathname();\n  const [expandedItems, setExpandedItems] = useState<string[]>([]);\n\n  const toggleExpanded = (title: string) => {\n    setExpandedItems(prev =>\n      prev.includes(title)\n        ? prev.filter(item => item !== title)\n        : [...prev, title]\n    );\n  };\n\n  // Early return if auth context is not ready\n  if (!hasPermission) {\n    return (\n      <div className=\"flex h-full flex-col bg-card border-r\">\n        <div className=\"flex h-16 items-center border-b px-6\">\n          <div className=\"flex items-center gap-2\">\n            <div className=\"w-8 h-8 bg-primary rounded-lg flex items-center justify-center\">\n              <span className=\"text-primary-foreground font-bold text-lg\">B</span>\n            </div>\n            <div>\n              <h2 className=\"text-lg font-semibold\">Benzochem</h2>\n              <p className=\"text-xs text-muted-foreground\">Admin Dashboard</p>\n            </div>\n          </div>\n        </div>\n        <div className=\"flex-1 flex items-center justify-center\">\n          <div className=\"text-sm text-muted-foreground\">Loading...</div>\n        </div>\n      </div>\n    );\n  }\n\n  const navItems: NavItem[] = [\n    {\n      title: \"Dashboard\",\n      href: \"/dashboard\",\n      icon: LayoutDashboard,\n    },\n    {\n      title: \"User Management\",\n      icon: Users,\n      permission: \"users.read\",\n      children: [\n        {\n          title: \"All Users\",\n          href: \"/dashboard/users\",\n          icon: Users,\n          permission: \"users.read\",\n        },\n        {\n          title: \"Pending Approvals\",\n          href: \"/dashboard/users/pending\",\n          icon: Users,\n          badge: \"3\", // This would come from real data\n          permission: \"users.approve\",\n        },\n      ],\n    },\n    {\n      title: \"Product Management\",\n      icon: Package,\n      permission: \"products.read\",\n      children: [\n        {\n          title: \"All Products\",\n          href: \"/dashboard/products\",\n          icon: Package,\n          permission: \"products.read\",\n        },\n        {\n          title: \"Featured Products\",\n          href: \"/dashboard/products/featured\",\n          icon: Package,\n          permission: \"products.read\",\n        },\n        {\n          title: \"Collections\",\n          href: \"/dashboard/products/collections\",\n          icon: Package,\n          permission: \"products.read\",\n        },\n      ],\n    },\n    {\n      title: \"Notifications\",\n      href: \"/dashboard/notifications\",\n      icon: Bell,\n      badge: \"5\", // This would come from real data\n      permission: \"notifications.read\",\n    },\n    {\n      title: \"Activity Logs\",\n      href: \"/dashboard/activity\",\n      icon: Activity,\n      permission: \"logs.read\",\n    },\n    {\n      title: \"System\",\n      icon: Settings,\n      permission: \"settings.read\",\n      children: [\n        {\n          title: \"Settings\",\n          href: \"/dashboard/settings\",\n          icon: Settings,\n          permission: \"settings.read\",\n        },\n        {\n          title: \"API Keys\",\n          href: \"/dashboard/api-keys\",\n          icon: Key,\n          permission: \"api_keys.read\",\n        },\n        {\n          title: \"Admin Users\",\n          href: \"/dashboard/admins\",\n          icon: UserCog,\n          permission: \"admins.read\",\n        },\n      ],\n    },\n  ];\n\n  const filteredNavItems = navItems.filter(item =>\n    !item.permission || (hasPermission && hasPermission(item.permission))\n  );\n\n  const renderNavItem = (item: NavItem, level = 0) => {\n    const hasChildren = item.children && item.children.length > 0;\n    const isExpanded = expandedItems.includes(item.title);\n    const isActive = item.href ? pathname === item.href : false;\n    const hasItemPermission = !item.permission || (hasPermission && hasPermission(item.permission));\n\n    if (!hasItemPermission) return null;\n\n    const filteredChildren = item.children?.filter(child =>\n      !child.permission || (hasPermission && hasPermission(child.permission))\n    );\n\n    if (hasChildren && (!filteredChildren || filteredChildren.length === 0)) {\n      return null;\n    }\n\n    const content = (\n      <div className={cn(\"space-y-1\", level > 0 && \"ml-4\")}>\n        <Button\n          variant={isActive ? \"secondary\" : \"ghost\"}\n          className={cn(\n            \"w-full justify-start gap-2 h-9\",\n            level > 0 && \"h-8 text-sm\"\n          )}\n          onClick={() => {\n            if (hasChildren) {\n              toggleExpanded(item.title);\n            } else if (item.href) {\n              onNavigate?.();\n            }\n          }}\n          asChild={!hasChildren}\n        >\n          {hasChildren ? (\n            <div className=\"flex items-center justify-between w-full\">\n              <div className=\"flex items-center gap-2\">\n                <item.icon className=\"h-4 w-4\" />\n                <span>{item.title}</span>\n                {item.badge && (\n                  <Badge variant=\"secondary\" className=\"ml-auto\">\n                    {item.badge}\n                  </Badge>\n                )}\n              </div>\n              {isExpanded ? (\n                <ChevronDown className=\"h-4 w-4\" />\n              ) : (\n                <ChevronRight className=\"h-4 w-4\" />\n              )}\n            </div>\n          ) : (\n            <Link href={item.href!} className=\"flex items-center gap-2 w-full\">\n              <item.icon className=\"h-4 w-4\" />\n              <span>{item.title}</span>\n              {item.badge && (\n                <Badge variant=\"secondary\" className=\"ml-auto\">\n                  {item.badge}\n                </Badge>\n              )}\n            </Link>\n          )}\n        </Button>\n\n        {hasChildren && isExpanded && filteredChildren && (\n          <div className=\"space-y-1\">\n            {filteredChildren.map((child) => (\n              <div key={child.title}>\n                {renderNavItem(child, level + 1)}\n              </div>\n            ))}\n          </div>\n        )}\n      </div>\n    );\n\n    return content;\n  };\n\n  return (\n    <div className=\"flex h-full flex-col bg-card border-r\">\n      {/* Logo */}\n      <div className=\"flex h-16 items-center border-b px-6\">\n        <div className=\"flex items-center gap-2\">\n          <div className=\"w-8 h-8 bg-primary rounded-lg flex items-center justify-center\">\n            <span className=\"text-primary-foreground font-bold text-lg\">B</span>\n          </div>\n          <div>\n            <h2 className=\"text-lg font-semibold\">Benzochem</h2>\n            <p className=\"text-xs text-muted-foreground\">Admin Dashboard</p>\n          </div>\n        </div>\n      </div>\n\n      {/* Navigation */}\n      <div className=\"flex-1 overflow-auto p-4\">\n        <nav className=\"space-y-2\">\n          {filteredNavItems.map((item) => (\n            <div key={item.title}>\n              {renderNavItem(item)}\n            </div>\n          ))}\n        </nav>\n      </div>\n\n      {/* User info and logout */}\n      <div className=\"border-t p-4\">\n        <div className=\"flex items-center gap-3 mb-3\">\n          <div className=\"w-8 h-8 bg-muted rounded-full flex items-center justify-center\">\n            <span className=\"text-sm font-medium\">\n              {admin?.firstName?.[0]}{admin?.lastName?.[0]}\n            </span>\n          </div>\n          <div className=\"flex-1 min-w-0\">\n            <p className=\"text-sm font-medium truncate\">\n              {admin?.firstName} {admin?.lastName}\n            </p>\n            <p className=\"text-xs text-muted-foreground truncate\">\n              {admin?.email}\n            </p>\n          </div>\n        </div>\n        <Button\n          variant=\"ghost\"\n          size=\"sm\"\n          className=\"w-full justify-start gap-2\"\n          onClick={logout}\n        >\n          <LogOut className=\"h-4 w-4\" />\n          Sign out\n        </Button>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AAEA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAaA;AACA;AACA;AAtBA;;;;;;;;;;AAqCO,SAAS,QAAQ,EAAE,UAAU,EAAgB;IAClD,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAC/C,MAAM,WAAW,CAAA,GAAA,kIAAA,CAAA,cAAW,AAAD;IAC3B,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IAE/D,MAAM,iBAAiB,CAAC;QACtB,iBAAiB,CAAA,OACf,KAAK,QAAQ,CAAC,SACV,KAAK,MAAM,CAAC,CAAA,OAAQ,SAAS,SAC7B;mBAAI;gBAAM;aAAM;IAExB;IAEA,4CAA4C;IAC5C,IAAI,CAAC,eAAe;QAClB,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;8CAA4C;;;;;;;;;;;0CAE9D,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAwB;;;;;;kDACtC,8OAAC;wCAAE,WAAU;kDAAgC;;;;;;;;;;;;;;;;;;;;;;;8BAInD,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;kCAAgC;;;;;;;;;;;;;;;;;IAIvD;IAEA,MAAM,WAAsB;QAC1B;YACE,OAAO;YACP,MAAM;YACN,MAAM,4NAAA,CAAA,kBAAe;QACvB;QACA;YACE,OAAO;YACP,MAAM,oMAAA,CAAA,QAAK;YACX,YAAY;YACZ,UAAU;gBACR;oBACE,OAAO;oBACP,MAAM;oBACN,MAAM,oMAAA,CAAA,QAAK;oBACX,YAAY;gBACd;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,MAAM,oMAAA,CAAA,QAAK;oBACX,OAAO;oBACP,YAAY;gBACd;aACD;QACH;QACA;YACE,OAAO;YACP,MAAM,wMAAA,CAAA,UAAO;YACb,YAAY;YACZ,UAAU;gBACR;oBACE,OAAO;oBACP,MAAM;oBACN,MAAM,wMAAA,CAAA,UAAO;oBACb,YAAY;gBACd;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,MAAM,wMAAA,CAAA,UAAO;oBACb,YAAY;gBACd;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,MAAM,wMAAA,CAAA,UAAO;oBACb,YAAY;gBACd;aACD;QACH;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,kMAAA,CAAA,OAAI;YACV,OAAO;YACP,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM;YACN,MAAM,0MAAA,CAAA,WAAQ;YACd,YAAY;QACd;QACA;YACE,OAAO;YACP,MAAM,0MAAA,CAAA,WAAQ;YACd,YAAY;YACZ,UAAU;gBACR;oBACE,OAAO;oBACP,MAAM;oBACN,MAAM,0MAAA,CAAA,WAAQ;oBACd,YAAY;gBACd;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,MAAM,gMAAA,CAAA,MAAG;oBACT,YAAY;gBACd;gBACA;oBACE,OAAO;oBACP,MAAM;oBACN,MAAM,4MAAA,CAAA,UAAO;oBACb,YAAY;gBACd;aACD;QACH;KACD;IAED,MAAM,mBAAmB,SAAS,MAAM,CAAC,CAAA,OACvC,CAAC,KAAK,UAAU,IAAK,iBAAiB,cAAc,KAAK,UAAU;IAGrE,MAAM,gBAAgB,CAAC,MAAe,QAAQ,CAAC;QAC7C,MAAM,cAAc,KAAK,QAAQ,IAAI,KAAK,QAAQ,CAAC,MAAM,GAAG;QAC5D,MAAM,aAAa,cAAc,QAAQ,CAAC,KAAK,KAAK;QACpD,MAAM,WAAW,KAAK,IAAI,GAAG,aAAa,KAAK,IAAI,GAAG;QACtD,MAAM,oBAAoB,CAAC,KAAK,UAAU,IAAK,iBAAiB,cAAc,KAAK,UAAU;QAE7F,IAAI,CAAC,mBAAmB,OAAO;QAE/B,MAAM,mBAAmB,KAAK,QAAQ,EAAE,OAAO,CAAA,QAC7C,CAAC,MAAM,UAAU,IAAK,iBAAiB,cAAc,MAAM,UAAU;QAGvE,IAAI,eAAe,CAAC,CAAC,oBAAoB,iBAAiB,MAAM,KAAK,CAAC,GAAG;YACvE,OAAO;QACT;QAEA,MAAM,wBACJ,8OAAC;YAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa,QAAQ,KAAK;;8BAC3C,8OAAC,kIAAA,CAAA,SAAM;oBACL,SAAS,WAAW,cAAc;oBAClC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kCACA,QAAQ,KAAK;oBAEf,SAAS;wBACP,IAAI,aAAa;4BACf,eAAe,KAAK,KAAK;wBAC3B,OAAO,IAAI,KAAK,IAAI,EAAE;4BACpB;wBACF;oBACF;oBACA,SAAS,CAAC;8BAET,4BACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,KAAK,IAAI;wCAAC,WAAU;;;;;;kDACrB,8OAAC;kDAAM,KAAK,KAAK;;;;;;oCAChB,KAAK,KAAK,kBACT,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAClC,KAAK,KAAK;;;;;;;;;;;;4BAIhB,2BACC,8OAAC,oNAAA,CAAA,cAAW;gCAAC,WAAU;;;;;qDAEvB,8OAAC,sNAAA,CAAA,eAAY;gCAAC,WAAU;;;;;;;;;;;6CAI5B,8OAAC,4JAAA,CAAA,UAAI;wBAAC,MAAM,KAAK,IAAI;wBAAG,WAAU;;0CAChC,8OAAC,KAAK,IAAI;gCAAC,WAAU;;;;;;0CACrB,8OAAC;0CAAM,KAAK,KAAK;;;;;;4BAChB,KAAK,KAAK,kBACT,8OAAC,iIAAA,CAAA,QAAK;gCAAC,SAAQ;gCAAY,WAAU;0CAClC,KAAK,KAAK;;;;;;;;;;;;;;;;;gBAOpB,eAAe,cAAc,kCAC5B,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,sBACrB,8OAAC;sCACE,cAAc,OAAO,QAAQ;2BADtB,MAAM,KAAK;;;;;;;;;;;;;;;;QAS/B,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAA4C;;;;;;;;;;;sCAE9D,8OAAC;;8CACC,8OAAC;oCAAG,WAAU;8CAAwB;;;;;;8CACtC,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;;;;;;0BAMnD,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;8BACZ,iBAAiB,GAAG,CAAC,CAAC,qBACrB,8OAAC;sCACE,cAAc;2BADP,KAAK,KAAK;;;;;;;;;;;;;;;0BAQ1B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAK,WAAU;;wCACb,OAAO,WAAW,CAAC,EAAE;wCAAE,OAAO,UAAU,CAAC,EAAE;;;;;;;;;;;;0CAGhD,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;;4CACV,OAAO;4CAAU;4CAAE,OAAO;;;;;;;kDAE7B,8OAAC;wCAAE,WAAU;kDACV,OAAO;;;;;;;;;;;;;;;;;;kCAId,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,WAAU;wBACV,SAAS;;0CAET,8OAAC,0MAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;4BAAY;;;;;;;;;;;;;;;;;;;AAMxC", "debugId": null}}, {"offset": {"line": 884, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/ui/dropdown-menu.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as DropdownMenuPrimitive from \"@radix-ui/react-dropdown-menu\"\nimport { CheckIcon, ChevronRightIcon, CircleIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction DropdownMenu({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Root>) {\n  return <DropdownMenuPrimitive.Root data-slot=\"dropdown-menu\" {...props} />\n}\n\nfunction DropdownMenuPortal({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Portal>) {\n  return (\n    <DropdownMenuPrimitive.Portal data-slot=\"dropdown-menu-portal\" {...props} />\n  )\n}\n\nfunction DropdownMenuTrigger({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Trigger>) {\n  return (\n    <DropdownMenuPrimitive.Trigger\n      data-slot=\"dropdown-menu-trigger\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuContent({\n  className,\n  sideOffset = 4,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Content>) {\n  return (\n    <DropdownMenuPrimitive.Portal>\n      <DropdownMenuPrimitive.Content\n        data-slot=\"dropdown-menu-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md\",\n          className\n        )}\n        {...props}\n      />\n    </DropdownMenuPrimitive.Portal>\n  )\n}\n\nfunction DropdownMenuGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Group>) {\n  return (\n    <DropdownMenuPrimitive.Group data-slot=\"dropdown-menu-group\" {...props} />\n  )\n}\n\nfunction DropdownMenuItem({\n  className,\n  inset,\n  variant = \"default\",\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Item> & {\n  inset?: boolean\n  variant?: \"default\" | \"destructive\"\n}) {\n  return (\n    <DropdownMenuPrimitive.Item\n      data-slot=\"dropdown-menu-item\"\n      data-inset={inset}\n      data-variant={variant}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuCheckboxItem({\n  className,\n  children,\n  checked,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.CheckboxItem>) {\n  return (\n    <DropdownMenuPrimitive.CheckboxItem\n      data-slot=\"dropdown-menu-checkbox-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      checked={checked}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.CheckboxItem>\n  )\n}\n\nfunction DropdownMenuRadioGroup({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioGroup>) {\n  return (\n    <DropdownMenuPrimitive.RadioGroup\n      data-slot=\"dropdown-menu-radio-group\"\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuRadioItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.RadioItem>) {\n  return (\n    <DropdownMenuPrimitive.RadioItem\n      data-slot=\"dropdown-menu-radio-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground relative flex cursor-default items-center gap-2 rounded-sm py-1.5 pr-2 pl-8 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"pointer-events-none absolute left-2 flex size-3.5 items-center justify-center\">\n        <DropdownMenuPrimitive.ItemIndicator>\n          <CircleIcon className=\"size-2 fill-current\" />\n        </DropdownMenuPrimitive.ItemIndicator>\n      </span>\n      {children}\n    </DropdownMenuPrimitive.RadioItem>\n  )\n}\n\nfunction DropdownMenuLabel({\n  className,\n  inset,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Label> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.Label\n      data-slot=\"dropdown-menu-label\"\n      data-inset={inset}\n      className={cn(\n        \"px-2 py-1.5 text-sm font-medium data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Separator>) {\n  return (\n    <DropdownMenuPrimitive.Separator\n      data-slot=\"dropdown-menu-separator\"\n      className={cn(\"bg-border -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuShortcut({\n  className,\n  ...props\n}: React.ComponentProps<\"span\">) {\n  return (\n    <span\n      data-slot=\"dropdown-menu-shortcut\"\n      className={cn(\n        \"text-muted-foreground ml-auto text-xs tracking-widest\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction DropdownMenuSub({\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.Sub>) {\n  return <DropdownMenuPrimitive.Sub data-slot=\"dropdown-menu-sub\" {...props} />\n}\n\nfunction DropdownMenuSubTrigger({\n  className,\n  inset,\n  children,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubTrigger> & {\n  inset?: boolean\n}) {\n  return (\n    <DropdownMenuPrimitive.SubTrigger\n      data-slot=\"dropdown-menu-sub-trigger\"\n      data-inset={inset}\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground data-[state=open]:bg-accent data-[state=open]:text-accent-foreground flex cursor-default items-center rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[inset]:pl-8\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <ChevronRightIcon className=\"ml-auto size-4\" />\n    </DropdownMenuPrimitive.SubTrigger>\n  )\n}\n\nfunction DropdownMenuSubContent({\n  className,\n  ...props\n}: React.ComponentProps<typeof DropdownMenuPrimitive.SubContent>) {\n  return (\n    <DropdownMenuPrimitive.SubContent\n      data-slot=\"dropdown-menu-sub-content\"\n      className={cn(\n        \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-hidden rounded-md border p-1 shadow-lg\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport {\n  DropdownMenu,\n  DropdownMenuPortal,\n  DropdownMenuTrigger,\n  DropdownMenuContent,\n  DropdownMenuGroup,\n  DropdownMenuLabel,\n  DropdownMenuItem,\n  DropdownMenuCheckboxItem,\n  DropdownMenuRadioGroup,\n  DropdownMenuRadioItem,\n  DropdownMenuSeparator,\n  DropdownMenuShortcut,\n  DropdownMenuSub,\n  DropdownMenuSubTrigger,\n  DropdownMenuSubContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,aAAa,EACpB,GAAG,OACqD;IACxD,qBAAO,8OAAC,4KAAA,CAAA,OAA0B;QAAC,aAAU;QAAiB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,mBAAmB,EAC1B,GAAG,OACuD;IAC1D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;QAAC,aAAU;QAAwB,GAAG,KAAK;;;;;;AAE5E;AAEA,SAAS,oBAAoB,EAC3B,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,UAA6B;QAC5B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,oBAAoB,EAC3B,SAAS,EACT,aAAa,CAAC,EACd,GAAG,OACwD;IAC3D,qBACE,8OAAC,4KAAA,CAAA,SAA4B;kBAC3B,cAAA,8OAAC,4KAAA,CAAA,UAA6B;YAC5B,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0jBACA;YAED,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,kBAAkB,EACzB,GAAG,OACsD;IACzD,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAAC,aAAU;QAAuB,GAAG,KAAK;;;;;;AAE1E;AAEA,SAAS,iBAAiB,EACxB,SAAS,EACT,KAAK,EACL,UAAU,SAAS,EACnB,GAAG,OAIJ;IACC,qBACE,8OAAC,4KAAA,CAAA,OAA0B;QACzB,aAAU;QACV,cAAY;QACZ,gBAAc;QACd,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+mBACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,yBAAyB,EAChC,SAAS,EACT,QAAQ,EACR,OAAO,EACP,GAAG,OAC6D;IAChE,qBACE,8OAAC,4KAAA,CAAA,eAAkC;QACjC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAEF,SAAS;QACR,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGxB;;;;;;;AAGP;AAEA,SAAS,uBAAuB,EAC9B,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACT,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,QAAQ,EACR,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gTACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,4KAAA,CAAA,gBAAmC;8BAClC,cAAA,8OAAC,0MAAA,CAAA,aAAU;wBAAC,WAAU;;;;;;;;;;;;;;;;YAGzB;;;;;;;AAGP;AAEA,SAAS,kBAAkB,EACzB,SAAS,EACT,KAAK,EACL,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,QAA2B;QAC1B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,sBAAsB,EAC7B,SAAS,EACT,GAAG,OAC0D;IAC7D,qBACE,8OAAC,4KAAA,CAAA,YAA+B;QAC9B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6BAA6B;QAC1C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OAC0B;IAC7B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,yDACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EACvB,GAAG,OACoD;IACvD,qBAAO,8OAAC,4KAAA,CAAA,MAAyB;QAAC,aAAU;QAAqB,GAAG,KAAK;;;;;;AAC3E;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,KAAK,EACL,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,cAAY;QACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kOACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,0NAAA,CAAA,mBAAgB;gBAAC,WAAU;;;;;;;;;;;;AAGlC;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,4KAAA,CAAA,aAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ifACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1146, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/ui/avatar.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as AvatarPrimitive from \"@radix-ui/react-avatar\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Avatar({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Root>) {\n  return (\n    <AvatarPrimitive.Root\n      data-slot=\"avatar\"\n      className={cn(\n        \"relative flex size-8 shrink-0 overflow-hidden rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarImage({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Image>) {\n  return (\n    <AvatarPrimitive.Image\n      data-slot=\"avatar-image\"\n      className={cn(\"aspect-square size-full\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction AvatarFallback({\n  className,\n  ...props\n}: React.ComponentProps<typeof AvatarPrimitive.Fallback>) {\n  return (\n    <AvatarPrimitive.Fallback\n      data-slot=\"avatar-fallback\"\n      className={cn(\n        \"bg-muted flex size-full items-center justify-center rounded-full\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nexport { Avatar, AvatarImage, AvatarFallback }\n"], "names": [], "mappings": ";;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,OAAO,EACd,SAAS,EACT,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2BAA2B;QACxC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,GAAG,OACmD;IACtD,qBACE,8OAAC,kKAAA,CAAA,WAAwB;QACvB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA;QAED,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 1198, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/theme-toggle.tsx"], "sourcesContent": ["\"use client\";\n\nimport * as React from \"react\";\nimport { Moon, Sun } from \"lucide-react\";\nimport { useTheme } from \"next-themes\";\n\nimport { Button } from \"@/components/ui/button\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\n\nexport function ThemeToggle() {\n  const { setTheme } = useTheme();\n\n  return (\n    <DropdownMenu>\n      <DropdownMenuTrigger asChild>\n        <Button variant=\"ghost\" size=\"icon\">\n          <Sun className=\"h-[1.2rem] w-[1.2rem] rotate-0 scale-100 transition-all dark:-rotate-90 dark:scale-0\" />\n          <Moon className=\"absolute h-[1.2rem] w-[1.2rem] rotate-90 scale-0 transition-all dark:rotate-0 dark:scale-100\" />\n          <span className=\"sr-only\">Toggle theme</span>\n        </Button>\n      </DropdownMenuTrigger>\n      <DropdownMenuContent align=\"end\">\n        <DropdownMenuItem onClick={() => setTheme(\"light\")}>\n          Light\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"dark\")}>\n          Dark\n        </DropdownMenuItem>\n        <DropdownMenuItem onClick={() => setTheme(\"system\")}>\n          System\n        </DropdownMenuItem>\n      </DropdownMenuContent>\n    </DropdownMenu>\n  );\n}\n"], "names": [], "mappings": ";;;;AAGA;AAAA;AACA;AAEA;AACA;AAPA;;;;;;AAcO,SAAS;IACd,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,gJAAA,CAAA,WAAQ,AAAD;IAE5B,qBACE,8OAAC,4IAAA,CAAA,eAAY;;0BACX,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAO;0BAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oBAAC,SAAQ;oBAAQ,MAAK;;sCAC3B,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;sCACf,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;sCAChB,8OAAC;4BAAK,WAAU;sCAAU;;;;;;;;;;;;;;;;;0BAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gBAAC,OAAM;;kCACzB,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAU;;;;;;kCAGpD,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAS;;;;;;kCAGnD,8OAAC,4IAAA,CAAA,mBAAgB;wBAAC,SAAS,IAAM,SAAS;kCAAW;;;;;;;;;;;;;;;;;;AAM7D", "debugId": null}}, {"offset": {"line": 1302, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/layout/header.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useAuth } from \"@/contexts/auth-context\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Badge } from \"@/components/ui/badge\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport { Avatar, AvatarFallback } from \"@/components/ui/avatar\";\nimport { ThemeToggle } from \"@/components/theme-toggle\";\nimport {\n  Bell,\n  Search,\n  Settings,\n  User,\n  LogOut,\n} from \"lucide-react\";\nimport { Input } from \"@/components/ui/input\";\n\nexport function Header() {\n  const { admin, logout } = useAuth();\n\n  return (\n    <header className=\"sticky top-0 z-30 flex h-16 items-center justify-between gap-4 border-b bg-background px-6\">\n      {/* Search */}\n      <div className=\"flex-1 max-w-md\">\n        <div className=\"relative\">\n          <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\n          <Input\n            placeholder=\"Search users, products, or settings...\"\n            className=\"pl-10\"\n          />\n        </div>\n      </div>\n\n      {/* Right side actions */}\n      <div className=\"flex items-center gap-2\">\n        {/* Notifications */}\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button variant=\"ghost\" size=\"icon\" className=\"relative\">\n              <Bell className=\"h-5 w-5\" />\n              <Badge \n                variant=\"destructive\" \n                className=\"absolute -top-1 -right-1 h-5 w-5 rounded-full p-0 text-xs\"\n              >\n                3\n              </Badge>\n            </Button>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent align=\"end\" className=\"w-80\">\n            <DropdownMenuLabel>Notifications</DropdownMenuLabel>\n            <DropdownMenuSeparator />\n            <div className=\"space-y-2 p-2\">\n              <div className=\"flex items-start space-x-3 p-2 rounded-lg hover:bg-muted\">\n                <div className=\"w-2 h-2 bg-blue-500 rounded-full mt-2\"></div>\n                <div className=\"flex-1 space-y-1\">\n                  <p className=\"text-sm font-medium\">New user registration</p>\n                  <p className=\"text-xs text-muted-foreground\">\n                    John Doe has registered and is pending approval\n                  </p>\n                  <p className=\"text-xs text-muted-foreground\">2 minutes ago</p>\n                </div>\n              </div>\n              <div className=\"flex items-start space-x-3 p-2 rounded-lg hover:bg-muted\">\n                <div className=\"w-2 h-2 bg-green-500 rounded-full mt-2\"></div>\n                <div className=\"flex-1 space-y-1\">\n                  <p className=\"text-sm font-medium\">Product updated</p>\n                  <p className=\"text-xs text-muted-foreground\">\n                    Benzene product information has been updated\n                  </p>\n                  <p className=\"text-xs text-muted-foreground\">5 minutes ago</p>\n                </div>\n              </div>\n              <div className=\"flex items-start space-x-3 p-2 rounded-lg hover:bg-muted\">\n                <div className=\"w-2 h-2 bg-yellow-500 rounded-full mt-2\"></div>\n                <div className=\"flex-1 space-y-1\">\n                  <p className=\"text-sm font-medium\">System alert</p>\n                  <p className=\"text-xs text-muted-foreground\">\n                    High number of pending user approvals\n                  </p>\n                  <p className=\"text-xs text-muted-foreground\">10 minutes ago</p>\n                </div>\n              </div>\n            </div>\n            <DropdownMenuSeparator />\n            <DropdownMenuItem className=\"w-full justify-center\">\n              View all notifications\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n\n        {/* Theme toggle */}\n        <ThemeToggle />\n\n        {/* User menu */}\n        <DropdownMenu>\n          <DropdownMenuTrigger asChild>\n            <Button variant=\"ghost\" className=\"relative h-9 w-9 rounded-full\">\n              <Avatar className=\"h-9 w-9\">\n                <AvatarFallback className=\"text-sm\">\n                  {admin?.firstName?.[0]}{admin?.lastName?.[0]}\n                </AvatarFallback>\n              </Avatar>\n            </Button>\n          </DropdownMenuTrigger>\n          <DropdownMenuContent className=\"w-56\" align=\"end\" forceMount>\n            <DropdownMenuLabel className=\"font-normal\">\n              <div className=\"flex flex-col space-y-1\">\n                <p className=\"text-sm font-medium leading-none\">\n                  {admin?.firstName} {admin?.lastName}\n                </p>\n                <p className=\"text-xs leading-none text-muted-foreground\">\n                  {admin?.email}\n                </p>\n                <Badge variant=\"outline\" className=\"w-fit mt-1\">\n                  Admin\n                </Badge>\n              </div>\n            </DropdownMenuLabel>\n            <DropdownMenuSeparator />\n            <DropdownMenuItem>\n              <User className=\"mr-2 h-4 w-4\" />\n              <span>Profile</span>\n            </DropdownMenuItem>\n            <DropdownMenuItem>\n              <Settings className=\"mr-2 h-4 w-4\" />\n              <span>Settings</span>\n            </DropdownMenuItem>\n            <DropdownMenuSeparator />\n            <DropdownMenuItem onClick={logout}>\n              <LogOut className=\"mr-2 h-4 w-4\" />\n              <span>Log out</span>\n            </DropdownMenuItem>\n          </DropdownMenuContent>\n        </DropdownMenu>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAQA;AACA;AACA;AAAA;AAAA;AAAA;AAAA;AAOA;AAtBA;;;;;;;;;;AAwBO,SAAS;IACd,MAAM,EAAE,KAAK,EAAE,MAAM,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAEhC,qBACE,8OAAC;QAAO,WAAU;;0BAEhB,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;sCAClB,8OAAC,iIAAA,CAAA,QAAK;4BACJ,aAAY;4BACZ,WAAU;;;;;;;;;;;;;;;;;0BAMhB,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC,4IAAA,CAAA,eAAY;;0CACX,8OAAC,4IAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,MAAK;oCAAO,WAAU;;sDAC5C,8OAAC,kMAAA,CAAA,OAAI;4CAAC,WAAU;;;;;;sDAChB,8OAAC,iIAAA,CAAA,QAAK;4CACJ,SAAQ;4CACR,WAAU;sDACX;;;;;;;;;;;;;;;;;0CAKL,8OAAC,4IAAA,CAAA,sBAAmB;gCAAC,OAAM;gCAAM,WAAU;;kDACzC,8OAAC,4IAAA,CAAA,oBAAiB;kDAAC;;;;;;kDACnB,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kDACtB,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;0EAG7C,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAGjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;0EAG7C,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;0DAGjD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAU;;;;;;kEACf,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAE,WAAU;0EAAsB;;;;;;0EACnC,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;0EAG7C,8OAAC;gEAAE,WAAU;0EAAgC;;;;;;;;;;;;;;;;;;;;;;;;kDAInD,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kDACtB,8OAAC,4IAAA,CAAA,mBAAgB;wCAAC,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;kCAOxD,8OAAC,qIAAA,CAAA,cAAW;;;;;kCAGZ,8OAAC,4IAAA,CAAA,eAAY;;0CACX,8OAAC,4IAAA,CAAA,sBAAmB;gCAAC,OAAO;0CAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAQ,WAAU;8CAChC,cAAA,8OAAC,kIAAA,CAAA,SAAM;wCAAC,WAAU;kDAChB,cAAA,8OAAC,kIAAA,CAAA,iBAAc;4CAAC,WAAU;;gDACvB,OAAO,WAAW,CAAC,EAAE;gDAAE,OAAO,UAAU,CAAC,EAAE;;;;;;;;;;;;;;;;;;;;;;0CAKpD,8OAAC,4IAAA,CAAA,sBAAmB;gCAAC,WAAU;gCAAO,OAAM;gCAAM,UAAU;;kDAC1D,8OAAC,4IAAA,CAAA,oBAAiB;wCAAC,WAAU;kDAC3B,cAAA,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAE,WAAU;;wDACV,OAAO;wDAAU;wDAAE,OAAO;;;;;;;8DAE7B,8OAAC;oDAAE,WAAU;8DACV,OAAO;;;;;;8DAEV,8OAAC,iIAAA,CAAA,QAAK;oDAAC,SAAQ;oDAAU,WAAU;8DAAa;;;;;;;;;;;;;;;;;kDAKpD,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kDACtB,8OAAC,4IAAA,CAAA,mBAAgB;;0DACf,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;0DAChB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,4IAAA,CAAA,mBAAgB;;0DACf,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;0DACpB,8OAAC;0DAAK;;;;;;;;;;;;kDAER,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kDACtB,8OAAC,4IAAA,CAAA,mBAAgB;wCAAC,SAAS;;0DACzB,8OAAC,0MAAA,CAAA,SAAM;gDAAC,WAAU;;;;;;0DAClB,8OAAC;0DAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOpB", "debugId": null}}, {"offset": {"line": 1798, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/layout/dashboard-layout.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { useAuth } from \"@/contexts/auth-context\";\nimport { But<PERSON> } from \"@/components/ui/button\";\nimport { Sheet, SheetContent, SheetTrigger } from \"@/components/ui/sheet\";\nimport { Sidebar } from \"./sidebar\";\nimport { Header } from \"./header\";\nimport { Menu } from \"lucide-react\";\n\ninterface DashboardLayoutProps {\n  children: React.ReactNode;\n}\n\nexport function DashboardLayout({ children }: DashboardLayoutProps) {\n  const [sidebarOpen, setSidebarOpen] = useState(false);\n  const { isAuthenticated } = useAuth();\n\n  if (!isAuthenticated) {\n    return null;\n  }\n\n  return (\n    <div className=\"min-h-screen bg-background\">\n      {/* Mobile sidebar */}\n      <Sheet open={sidebarOpen} onOpenChange={setSidebarOpen}>\n        <SheetTrigger asChild>\n          <Button\n            variant=\"ghost\"\n            size=\"icon\"\n            className=\"fixed top-4 left-4 z-40 md:hidden\"\n          >\n            <Menu className=\"h-5 w-5\" />\n          </Button>\n        </SheetTrigger>\n        <SheetContent side=\"left\" className=\"p-0 w-72\">\n          <Sidebar onNavigate={() => setSidebarOpen(false)} />\n        </SheetContent>\n      </Sheet>\n\n      {/* Desktop sidebar */}\n      <div className=\"hidden md:fixed md:inset-y-0 md:flex md:w-72 md:flex-col\">\n        <Sidebar />\n      </div>\n\n      {/* Main content */}\n      <div className=\"md:pl-72\">\n        <Header />\n        <main className=\"p-6\">\n          {children}\n        </main>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AARA;;;;;;;;;AAcO,SAAS,gBAAgB,EAAE,QAAQ,EAAwB;IAChE,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,EAAE,eAAe,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAElC,IAAI,CAAC,iBAAiB;QACpB,OAAO;IACT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC,iIAAA,CAAA,QAAK;gBAAC,MAAM;gBAAa,cAAc;;kCACtC,8OAAC,iIAAA,CAAA,eAAY;wBAAC,OAAO;kCACnB,cAAA,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,MAAK;4BACL,WAAU;sCAEV,cAAA,8OAAC,kMAAA,CAAA,OAAI;gCAAC,WAAU;;;;;;;;;;;;;;;;kCAGpB,8OAAC,iIAAA,CAAA,eAAY;wBAAC,MAAK;wBAAO,WAAU;kCAClC,cAAA,8OAAC,uIAAA,CAAA,UAAO;4BAAC,YAAY,IAAM,eAAe;;;;;;;;;;;;;;;;;0BAK9C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,uIAAA,CAAA,UAAO;;;;;;;;;;0BAIV,8OAAC;gBAAI,WAAU;;kCACb,8OAAC,sIAAA,CAAA,SAAM;;;;;kCACP,8OAAC;wBAAK,WAAU;kCACb;;;;;;;;;;;;;;;;;;AAKX", "debugId": null}}, {"offset": {"line": 1922, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2019, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/ui/table.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Table({ className, ...props }: React.ComponentProps<\"table\">) {\n  return (\n    <div\n      data-slot=\"table-container\"\n      className=\"relative w-full overflow-x-auto\"\n    >\n      <table\n        data-slot=\"table\"\n        className={cn(\"w-full caption-bottom text-sm\", className)}\n        {...props}\n      />\n    </div>\n  )\n}\n\nfunction TableHeader({ className, ...props }: React.ComponentProps<\"thead\">) {\n  return (\n    <thead\n      data-slot=\"table-header\"\n      className={cn(\"[&_tr]:border-b\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableBody({ className, ...props }: React.ComponentProps<\"tbody\">) {\n  return (\n    <tbody\n      data-slot=\"table-body\"\n      className={cn(\"[&_tr:last-child]:border-0\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction TableFooter({ className, ...props }: React.ComponentProps<\"tfoot\">) {\n  return (\n    <tfoot\n      data-slot=\"table-footer\"\n      className={cn(\n        \"bg-muted/50 border-t font-medium [&>tr]:last:border-b-0\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableRow({ className, ...props }: React.ComponentProps<\"tr\">) {\n  return (\n    <tr\n      data-slot=\"table-row\"\n      className={cn(\n        \"hover:bg-muted/50 data-[state=selected]:bg-muted border-b transition-colors\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableHead({ className, ...props }: React.ComponentProps<\"th\">) {\n  return (\n    <th\n      data-slot=\"table-head\"\n      className={cn(\n        \"text-foreground h-10 px-2 text-left align-middle font-medium whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCell({ className, ...props }: React.ComponentProps<\"td\">) {\n  return (\n    <td\n      data-slot=\"table-cell\"\n      className={cn(\n        \"p-2 align-middle whitespace-nowrap [&:has([role=checkbox])]:pr-0 [&>[role=checkbox]]:translate-y-[2px]\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction TableCaption({\n  className,\n  ...props\n}: React.ComponentProps<\"caption\">) {\n  return (\n    <caption\n      data-slot=\"table-caption\"\n      className={cn(\"text-muted-foreground mt-4 text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Table,\n  TableHeader,\n  TableBody,\n  TableFooter,\n  TableHead,\n  TableRow,\n  TableCell,\n  TableCaption,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAIA;AAJA;;;AAMA,SAAS,MAAM,EAAE,SAAS,EAAE,GAAG,OAAsC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAU;kBAEV,cAAA,8OAAC;YACC,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;YAC9C,GAAG,KAAK;;;;;;;;;;;AAIjB;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,mBAAmB;QAChC,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAsC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAsC;IACzE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,2DACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,SAAS,EAAE,SAAS,EAAE,GAAG,OAAmC;IACnE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+EACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sJACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAmC;IACpE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0GACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,aAAa,EACpB,SAAS,EACT,GAAG,OAC6B;IAChC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sCAAsC;QACnD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 2137, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/ui/select.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as SelectPrimitive from \"@radix-ui/react-select\"\nimport { Check<PERSON><PERSON>, ChevronDownIcon, ChevronUpIcon } from \"lucide-react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Select({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Root>) {\n  return <SelectPrimitive.Root data-slot=\"select\" {...props} />\n}\n\nfunction SelectGroup({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Group>) {\n  return <SelectPrimitive.Group data-slot=\"select-group\" {...props} />\n}\n\nfunction SelectValue({\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Value>) {\n  return <SelectPrimitive.Value data-slot=\"select-value\" {...props} />\n}\n\nfunction SelectTrigger({\n  className,\n  size = \"default\",\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Trigger> & {\n  size?: \"sm\" | \"default\"\n}) {\n  return (\n    <SelectPrimitive.Trigger\n      data-slot=\"select-trigger\"\n      data-size={size}\n      className={cn(\n        \"border-input data-[placeholder]:text-muted-foreground [&_svg:not([class*='text-'])]:text-muted-foreground focus-visible:border-ring focus-visible:ring-ring/50 aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive dark:bg-input/30 dark:hover:bg-input/50 flex w-fit items-center justify-between gap-2 rounded-md border bg-transparent px-3 py-2 text-sm whitespace-nowrap shadow-xs transition-[color,box-shadow] outline-none focus-visible:ring-[3px] disabled:cursor-not-allowed disabled:opacity-50 data-[size=default]:h-9 data-[size=sm]:h-8 *:data-[slot=select-value]:line-clamp-1 *:data-[slot=select-value]:flex *:data-[slot=select-value]:items-center *:data-[slot=select-value]:gap-2 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4\",\n        className\n      )}\n      {...props}\n    >\n      {children}\n      <SelectPrimitive.Icon asChild>\n        <ChevronDownIcon className=\"size-4 opacity-50\" />\n      </SelectPrimitive.Icon>\n    </SelectPrimitive.Trigger>\n  )\n}\n\nfunction SelectContent({\n  className,\n  children,\n  position = \"popper\",\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Content>) {\n  return (\n    <SelectPrimitive.Portal>\n      <SelectPrimitive.Content\n        data-slot=\"select-content\"\n        className={cn(\n          \"bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 relative z-50 max-h-(--radix-select-content-available-height) min-w-[8rem] origin-(--radix-select-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border shadow-md\",\n          position === \"popper\" &&\n            \"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1\",\n          className\n        )}\n        position={position}\n        {...props}\n      >\n        <SelectScrollUpButton />\n        <SelectPrimitive.Viewport\n          className={cn(\n            \"p-1\",\n            position === \"popper\" &&\n              \"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)] scroll-my-1\"\n          )}\n        >\n          {children}\n        </SelectPrimitive.Viewport>\n        <SelectScrollDownButton />\n      </SelectPrimitive.Content>\n    </SelectPrimitive.Portal>\n  )\n}\n\nfunction SelectLabel({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Label>) {\n  return (\n    <SelectPrimitive.Label\n      data-slot=\"select-label\"\n      className={cn(\"text-muted-foreground px-2 py-1.5 text-xs\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectItem({\n  className,\n  children,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Item>) {\n  return (\n    <SelectPrimitive.Item\n      data-slot=\"select-item\"\n      className={cn(\n        \"focus:bg-accent focus:text-accent-foreground [&_svg:not([class*='text-'])]:text-muted-foreground relative flex w-full cursor-default items-center gap-2 rounded-sm py-1.5 pr-8 pl-2 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4 *:[span]:last:flex *:[span]:last:items-center *:[span]:last:gap-2\",\n        className\n      )}\n      {...props}\n    >\n      <span className=\"absolute right-2 flex size-3.5 items-center justify-center\">\n        <SelectPrimitive.ItemIndicator>\n          <CheckIcon className=\"size-4\" />\n        </SelectPrimitive.ItemIndicator>\n      </span>\n      <SelectPrimitive.ItemText>{children}</SelectPrimitive.ItemText>\n    </SelectPrimitive.Item>\n  )\n}\n\nfunction SelectSeparator({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.Separator>) {\n  return (\n    <SelectPrimitive.Separator\n      data-slot=\"select-separator\"\n      className={cn(\"bg-border pointer-events-none -mx-1 my-1 h-px\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction SelectScrollUpButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollUpButton>) {\n  return (\n    <SelectPrimitive.ScrollUpButton\n      data-slot=\"select-scroll-up-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronUpIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollUpButton>\n  )\n}\n\nfunction SelectScrollDownButton({\n  className,\n  ...props\n}: React.ComponentProps<typeof SelectPrimitive.ScrollDownButton>) {\n  return (\n    <SelectPrimitive.ScrollDownButton\n      data-slot=\"select-scroll-down-button\"\n      className={cn(\n        \"flex cursor-default items-center justify-center py-1\",\n        className\n      )}\n      {...props}\n    >\n      <ChevronDownIcon className=\"size-4\" />\n    </SelectPrimitive.ScrollDownButton>\n  )\n}\n\nexport {\n  Select,\n  SelectContent,\n  SelectGroup,\n  SelectItem,\n  SelectLabel,\n  SelectScrollDownButton,\n  SelectScrollUpButton,\n  SelectSeparator,\n  SelectTrigger,\n  SelectValue,\n}\n"], "names": [], "mappings": ";;;;;;;;;;;;;AAGA;AACA;AAAA;AAAA;AAEA;AANA;;;;;AAQA,SAAS,OAAO,EACd,GAAG,OAC+C;IAClD,qBAAO,8OAAC,kKAAA,CAAA,OAAoB;QAAC,aAAU;QAAU,GAAG,KAAK;;;;;;AAC3D;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,YAAY,EACnB,GAAG,OACgD;IACnD,qBAAO,8OAAC,kKAAA,CAAA,QAAqB;QAAC,aAAU;QAAgB,GAAG,KAAK;;;;;;AAClE;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,OAAO,SAAS,EAChB,QAAQ,EACR,GAAG,OAGJ;IACC,qBACE,8OAAC,kKAAA,CAAA,UAAuB;QACtB,aAAU;QACV,aAAW;QACX,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,gzBACA;QAED,GAAG,KAAK;;YAER;0BACD,8OAAC,kKAAA,CAAA,OAAoB;gBAAC,OAAO;0BAC3B,cAAA,8OAAC,wNAAA,CAAA,kBAAe;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAInC;AAEA,SAAS,cAAc,EACrB,SAAS,EACT,QAAQ,EACR,WAAW,QAAQ,EACnB,GAAG,OACkD;IACrD,qBACE,8OAAC,kKAAA,CAAA,SAAsB;kBACrB,cAAA,8OAAC,kKAAA,CAAA,UAAuB;YACtB,aAAU;YACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,ijBACA,aAAa,YACX,mIACF;YAEF,UAAU;YACT,GAAG,KAAK;;8BAET,8OAAC;;;;;8BACD,8OAAC,kKAAA,CAAA,WAAwB;oBACvB,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,OACA,aAAa,YACX;8BAGH;;;;;;8BAEH,8OAAC;;;;;;;;;;;;;;;;AAIT;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACgD;IACnD,qBACE,8OAAC,kKAAA,CAAA,QAAqB;QACpB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,6CAA6C;QAC1D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,QAAQ,EACR,GAAG,OAC+C;IAClD,qBACE,8OAAC,kKAAA,CAAA,OAAoB;QACnB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,6aACA;QAED,GAAG,KAAK;;0BAET,8OAAC;gBAAK,WAAU;0BACd,cAAA,8OAAC,kKAAA,CAAA,gBAA6B;8BAC5B,cAAA,8OAAC,wMAAA,CAAA,YAAS;wBAAC,WAAU;;;;;;;;;;;;;;;;0BAGzB,8OAAC,kKAAA,CAAA,WAAwB;0BAAE;;;;;;;;;;;;AAGjC;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACoD;IACvD,qBACE,8OAAC,kKAAA,CAAA,YAAyB;QACxB,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iDAAiD;QAC9D,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,qBAAqB,EAC5B,SAAS,EACT,GAAG,OACyD;IAC5D,qBACE,8OAAC,kKAAA,CAAA,iBAA8B;QAC7B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,oNAAA,CAAA,gBAAa;YAAC,WAAU;;;;;;;;;;;AAG/B;AAEA,SAAS,uBAAuB,EAC9B,SAAS,EACT,GAAG,OAC2D;IAC9D,qBACE,8OAAC,kKAAA,CAAA,mBAAgC;QAC/B,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,wDACA;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,wNAAA,CAAA,kBAAe;YAAC,WAAU;;;;;;;;;;;AAGjC", "debugId": null}}, {"offset": {"line": 2362, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/ui/enhanced-dialog.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { cn } from \"@/lib/utils\"\nimport {\n  <PERSON>alog,\n  DialogContent,\n  DialogDescription,\n  DialogFooter,\n  DialogHeader,\n  DialogTitle,\n} from \"./dialog\"\nimport { <PERSON><PERSON> } from \"./button\"\nimport { Loader2 } from \"lucide-react\"\n\ninterface EnhancedDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  title: string\n  description?: string\n  children: React.ReactNode\n  footer?: React.ReactNode\n  className?: string\n  size?: \"sm\" | \"md\" | \"lg\" | \"xl\" | \"2xl\" | \"3xl\" | \"4xl\" | \"5xl\"\n  loading?: boolean\n}\n\nconst sizeClasses = {\n  sm: \"w-full max-w-sm mx-4 sm:mx-auto\",\n  md: \"w-full max-w-md mx-4 sm:mx-auto\",\n  lg: \"w-full max-w-lg mx-4 sm:mx-auto\",\n  xl: \"w-full max-w-xl mx-4 sm:mx-auto\",\n  \"2xl\": \"w-full max-w-2xl mx-4 sm:mx-auto\",\n  \"3xl\": \"w-full max-w-3xl mx-4 sm:mx-auto\",\n  \"4xl\": \"w-full max-w-4xl mx-4 sm:mx-auto\",\n  \"5xl\": \"w-full max-w-5xl mx-4 sm:mx-auto\",\n}\n\nexport function EnhancedDialog({\n  open,\n  onOpenChange,\n  title,\n  description,\n  children,\n  footer,\n  className,\n  size = \"lg\",\n  loading = false,\n}: EnhancedDialogProps) {\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent\n        className={cn(\n          sizeClasses[size],\n          \"max-h-[90vh] overflow-hidden flex flex-col\",\n          className\n        )}\n        aria-labelledby=\"dialog-title\"\n        aria-describedby={description ? \"dialog-description\" : undefined}\n        role=\"dialog\"\n        aria-modal=\"true\"\n      >\n        <DialogHeader>\n          <DialogTitle\n            id=\"dialog-title\"\n            className=\"flex items-center gap-2\"\n          >\n            {loading && (\n              <Loader2\n                className=\"h-4 w-4 animate-spin\"\n                aria-hidden=\"true\"\n              />\n            )}\n            {title || \"Dialog\"}\n          </DialogTitle>\n          {description && (\n            <DialogDescription id=\"dialog-description\">\n              {description}\n            </DialogDescription>\n          )}\n        </DialogHeader>\n\n        <div\n          className=\"flex-1 overflow-y-auto px-4 py-4 sm:px-6\"\n          role=\"main\"\n          tabIndex={-1}\n        >\n          {children}\n        </div>\n\n        {footer && (\n          <DialogFooter role=\"group\" aria-label=\"Dialog actions\">\n            {footer}\n          </DialogFooter>\n        )}\n      </DialogContent>\n    </Dialog>\n  )\n}\n\ninterface FormSectionProps {\n  title: string\n  description?: string\n  children: React.ReactNode\n  className?: string\n}\n\nexport function FormSection({ title, description, children, className }: FormSectionProps) {\n  return (\n    <div className={cn(\"space-y-4 animate-in fade-in-0 slide-in-from-bottom-2 duration-500\", className)}>\n      <div className=\"space-y-1\">\n        <h3 className=\"text-lg font-semibold leading-none tracking-tight border-b border-border/50 pb-3 transition-colors duration-200\">\n          {title}\n        </h3>\n        {description && (\n          <p className=\"text-sm text-muted-foreground leading-relaxed transition-colors duration-200\">\n            {description}\n          </p>\n        )}\n      </div>\n      <div className=\"space-y-4 pt-2\">\n        {children}\n      </div>\n    </div>\n  )\n}\n\ninterface FormFieldProps {\n  label: string\n  description?: string\n  required?: boolean\n  error?: string\n  children: React.ReactNode\n  className?: string\n}\n\nexport function FormField({\n  label,\n  description,\n  required = false,\n  error,\n  children,\n  className\n}: FormFieldProps) {\n  const fieldId = React.useId()\n  const errorId = error ? `${fieldId}-error` : undefined\n  const descriptionId = description ? `${fieldId}-description` : undefined\n\n  return (\n    <div className={cn(\"space-y-2 animate-in fade-in-0 slide-in-from-left-1 duration-300\", className)}>\n      <div className=\"space-y-1\">\n        <label\n          htmlFor={fieldId}\n          className=\"text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70 transition-colors duration-200\"\n        >\n          {label}\n          {required && (\n            <>\n              <span className=\"text-destructive ml-1\" aria-hidden=\"true\">*</span>\n              <span className=\"sr-only\">(required)</span>\n            </>\n          )}\n        </label>\n        {description && (\n          <p\n            id={descriptionId}\n            className=\"text-xs text-muted-foreground leading-relaxed\"\n          >\n            {description}\n          </p>\n        )}\n      </div>\n      {React.cloneElement(children as React.ReactElement<any>, {\n        id: fieldId,\n        'aria-describedby': [descriptionId, errorId].filter(Boolean).join(' ') || undefined,\n        'aria-invalid': !!error,\n        'aria-required': required,\n      })}\n      {error && (\n        <p\n          id={errorId}\n          className=\"text-xs text-destructive leading-relaxed\"\n          role=\"alert\"\n          aria-live=\"polite\"\n        >\n          {error}\n        </p>\n      )}\n    </div>\n  )\n}\n\ninterface ConfirmDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  title: string\n  description: string\n  confirmText?: string\n  cancelText?: string\n  onConfirm: () => void\n  onCancel?: () => void\n  variant?: \"default\" | \"destructive\"\n  loading?: boolean\n}\n\nexport function ConfirmDialog({\n  open,\n  onOpenChange,\n  title,\n  description,\n  confirmText = \"Confirm\",\n  cancelText = \"Cancel\",\n  onConfirm,\n  onCancel,\n  variant = \"default\",\n  loading = false,\n}: ConfirmDialogProps) {\n  const handleCancel = () => {\n    if (onCancel) {\n      onCancel()\n    } else {\n      onOpenChange(false)\n    }\n  }\n\n  const handleConfirm = () => {\n    onConfirm()\n  }\n\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent\n        className=\"w-full max-w-md mx-4 sm:mx-auto\"\n        aria-labelledby=\"confirm-dialog-title\"\n        aria-describedby=\"confirm-dialog-description\"\n        role=\"alertdialog\"\n        aria-modal=\"true\"\n      >\n        <DialogHeader>\n          <DialogTitle id=\"confirm-dialog-title\">\n            {title || \"Confirm Action\"}\n          </DialogTitle>\n          <DialogDescription\n            id=\"confirm-dialog-description\"\n            className=\"leading-relaxed\"\n          >\n            {description}\n          </DialogDescription>\n        </DialogHeader>\n\n        <DialogFooter role=\"group\" aria-label=\"Confirmation actions\">\n          <Button\n            variant=\"outline\"\n            onClick={handleCancel}\n            disabled={loading}\n            aria-label={`${cancelText} this action`}\n          >\n            {cancelText}\n          </Button>\n          <Button\n            variant={variant}\n            onClick={handleConfirm}\n            disabled={loading}\n            aria-label={`${confirmText} this action`}\n            autoFocus\n          >\n            {loading && (\n              <Loader2\n                className=\"h-4 w-4 animate-spin mr-2\"\n                aria-hidden=\"true\"\n              />\n            )}\n            {confirmText}\n          </Button>\n        </DialogFooter>\n      </DialogContent>\n    </Dialog>\n  )\n}\n\ninterface DetailDialogProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  title: string\n  subtitle?: string\n  children: React.ReactNode\n  actions?: React.ReactNode\n  size?: \"sm\" | \"md\" | \"lg\" | \"xl\" | \"2xl\" | \"3xl\" | \"4xl\" | \"5xl\"\n}\n\nexport function DetailDialog({\n  open,\n  onOpenChange,\n  title,\n  subtitle,\n  children,\n  actions,\n  size = \"2xl\",\n}: DetailDialogProps) {\n  return (\n    <Dialog open={open} onOpenChange={onOpenChange}>\n      <DialogContent\n        className={cn(\n          sizeClasses[size],\n          \"max-h-[90vh] overflow-hidden flex flex-col\"\n        )}\n        aria-labelledby=\"detail-dialog-title\"\n        aria-describedby={subtitle ? \"detail-dialog-subtitle\" : undefined}\n        role=\"dialog\"\n        aria-modal=\"true\"\n      >\n        <DialogHeader>\n          <DialogTitle id=\"detail-dialog-title\">\n            {title || \"Details\"}\n          </DialogTitle>\n          {subtitle && (\n            <DialogDescription id=\"detail-dialog-subtitle\">\n              {subtitle}\n            </DialogDescription>\n          )}\n        </DialogHeader>\n\n        <div\n          className=\"flex-1 overflow-y-auto px-4 py-4 sm:px-6\"\n          role=\"main\"\n          tabIndex={-1}\n          aria-label=\"Detail content\"\n        >\n          {children}\n        </div>\n\n        {actions && (\n          <DialogFooter role=\"group\" aria-label=\"Detail actions\">\n            {actions}\n          </DialogFooter>\n        )}\n      </DialogContent>\n    </Dialog>\n  )\n}\n\ninterface DetailSectionProps {\n  title: string\n  children: React.ReactNode\n  className?: string\n  columns?: 1 | 2 | 3\n}\n\nexport function DetailSection({ title, children, className, columns = 2 }: DetailSectionProps) {\n  const sectionId = React.useId()\n  const gridClasses = {\n    1: \"grid-cols-1\",\n    2: \"grid-cols-1 sm:grid-cols-2\",\n    3: \"grid-cols-1 sm:grid-cols-2 lg:grid-cols-3\",\n  }\n\n  return (\n    <section\n      className={cn(\"space-y-4\", className)}\n      aria-labelledby={`${sectionId}-title`}\n    >\n      <h4\n        id={`${sectionId}-title`}\n        className=\"font-semibold text-lg border-b border-border/30 pb-2\"\n      >\n        {title}\n      </h4>\n      <div\n        className={cn(\"grid gap-4 animate-in fade-in-0 slide-in-from-bottom-2 duration-300\", gridClasses[columns])}\n        role=\"group\"\n        aria-labelledby={`${sectionId}-title`}\n      >\n        {children}\n      </div>\n    </section>\n  )\n}\n\ninterface DetailFieldProps {\n  label: string\n  value: React.ReactNode\n  className?: string\n}\n\nexport function DetailField({ label, value, className }: DetailFieldProps) {\n  const fieldId = React.useId()\n\n  return (\n    <div className={cn(\"space-y-1 group hover:bg-muted/30 rounded-md p-2 -m-2 transition-colors duration-200\", className)}>\n      <label\n        id={`${fieldId}-label`}\n        className=\"text-sm font-medium text-muted-foreground transition-colors duration-200 group-hover:text-foreground\"\n      >\n        {label}\n      </label>\n      <div\n        className=\"text-sm text-foreground transition-all duration-200\"\n        aria-labelledby={`${fieldId}-label`}\n        role=\"text\"\n      >\n        {value || (\n          <span className=\"text-muted-foreground italic\" aria-label={`${label} not provided`}>\n            Not provided\n          </span>\n        )}\n      </div>\n    </div>\n  )\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;AACA;AACA;AAQA;AACA;AAbA;;;;;;;AA2BA,MAAM,cAAc;IAClB,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,IAAI;IACJ,OAAO;IACP,OAAO;IACP,OAAO;IACP,OAAO;AACT;AAEO,SAAS,eAAe,EAC7B,IAAI,EACJ,YAAY,EACZ,KAAK,EACL,WAAW,EACX,QAAQ,EACR,MAAM,EACN,SAAS,EACT,OAAO,IAAI,EACX,UAAU,KAAK,EACK;IACpB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,WAAW,CAAC,KAAK,EACjB,8CACA;YAEF,mBAAgB;YAChB,oBAAkB,cAAc,uBAAuB;YACvD,MAAK;YACL,cAAW;;8BAEX,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BACV,IAAG;4BACH,WAAU;;gCAET,yBACC,8OAAC,iNAAA,CAAA,UAAO;oCACN,WAAU;oCACV,eAAY;;;;;;gCAGf,SAAS;;;;;;;wBAEX,6BACC,8OAAC,kIAAA,CAAA,oBAAiB;4BAAC,IAAG;sCACnB;;;;;;;;;;;;8BAKP,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,UAAU,CAAC;8BAEV;;;;;;gBAGF,wBACC,8OAAC,kIAAA,CAAA,eAAY;oBAAC,MAAK;oBAAQ,cAAW;8BACnC;;;;;;;;;;;;;;;;;AAMb;AASO,SAAS,YAAY,EAAE,KAAK,EAAE,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAoB;IACvF,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,sEAAsE;;0BACvF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCACX;;;;;;oBAEF,6BACC,8OAAC;wBAAE,WAAU;kCACV;;;;;;;;;;;;0BAIP,8OAAC;gBAAI,WAAU;0BACZ;;;;;;;;;;;;AAIT;AAWO,SAAS,UAAU,EACxB,KAAK,EACL,WAAW,EACX,WAAW,KAAK,EAChB,KAAK,EACL,QAAQ,EACR,SAAS,EACM;IACf,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAC1B,MAAM,UAAU,QAAQ,GAAG,QAAQ,MAAM,CAAC,GAAG;IAC7C,MAAM,gBAAgB,cAAc,GAAG,QAAQ,YAAY,CAAC,GAAG;IAE/D,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,oEAAoE;;0BACrF,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBACC,SAAS;wBACT,WAAU;;4BAET;4BACA,0BACC;;kDACE,8OAAC;wCAAK,WAAU;wCAAwB,eAAY;kDAAO;;;;;;kDAC3D,8OAAC;wCAAK,WAAU;kDAAU;;;;;;;;;;;;;;oBAI/B,6BACC,8OAAC;wBACC,IAAI;wBACJ,WAAU;kCAET;;;;;;;;;;;;0BAIN,CAAA,GAAA,qMAAA,CAAA,eAAkB,AAAD,EAAE,UAAqC;gBACvD,IAAI;gBACJ,oBAAoB;oBAAC;oBAAe;iBAAQ,CAAC,MAAM,CAAC,SAAS,IAAI,CAAC,QAAQ;gBAC1E,gBAAgB,CAAC,CAAC;gBAClB,iBAAiB;YACnB;YACC,uBACC,8OAAC;gBACC,IAAI;gBACJ,WAAU;gBACV,MAAK;gBACL,aAAU;0BAET;;;;;;;;;;;;AAKX;AAeO,SAAS,cAAc,EAC5B,IAAI,EACJ,YAAY,EACZ,KAAK,EACL,WAAW,EACX,cAAc,SAAS,EACvB,aAAa,QAAQ,EACrB,SAAS,EACT,QAAQ,EACR,UAAU,SAAS,EACnB,UAAU,KAAK,EACI;IACnB,MAAM,eAAe;QACnB,IAAI,UAAU;YACZ;QACF,OAAO;YACL,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB;QACpB;IACF;IAEA,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YACZ,WAAU;YACV,mBAAgB;YAChB,oBAAiB;YACjB,MAAK;YACL,cAAW;;8BAEX,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,IAAG;sCACb,SAAS;;;;;;sCAEZ,8OAAC,kIAAA,CAAA,oBAAiB;4BAChB,IAAG;4BACH,WAAU;sCAET;;;;;;;;;;;;8BAIL,8OAAC,kIAAA,CAAA,eAAY;oBAAC,MAAK;oBAAQ,cAAW;;sCACpC,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAQ;4BACR,SAAS;4BACT,UAAU;4BACV,cAAY,GAAG,WAAW,YAAY,CAAC;sCAEtC;;;;;;sCAEH,8OAAC,kIAAA,CAAA,SAAM;4BACL,SAAS;4BACT,SAAS;4BACT,UAAU;4BACV,cAAY,GAAG,YAAY,YAAY,CAAC;4BACxC,SAAS;;gCAER,yBACC,8OAAC,iNAAA,CAAA,UAAO;oCACN,WAAU;oCACV,eAAY;;;;;;gCAGf;;;;;;;;;;;;;;;;;;;;;;;;AAMb;AAYO,SAAS,aAAa,EAC3B,IAAI,EACJ,YAAY,EACZ,KAAK,EACL,QAAQ,EACR,QAAQ,EACR,OAAO,EACP,OAAO,KAAK,EACM;IAClB,qBACE,8OAAC,kIAAA,CAAA,SAAM;QAAC,MAAM;QAAM,cAAc;kBAChC,cAAA,8OAAC,kIAAA,CAAA,gBAAa;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,WAAW,CAAC,KAAK,EACjB;YAEF,mBAAgB;YAChB,oBAAkB,WAAW,2BAA2B;YACxD,MAAK;YACL,cAAW;;8BAEX,8OAAC,kIAAA,CAAA,eAAY;;sCACX,8OAAC,kIAAA,CAAA,cAAW;4BAAC,IAAG;sCACb,SAAS;;;;;;wBAEX,0BACC,8OAAC,kIAAA,CAAA,oBAAiB;4BAAC,IAAG;sCACnB;;;;;;;;;;;;8BAKP,8OAAC;oBACC,WAAU;oBACV,MAAK;oBACL,UAAU,CAAC;oBACX,cAAW;8BAEV;;;;;;gBAGF,yBACC,8OAAC,kIAAA,CAAA,eAAY;oBAAC,MAAK;oBAAQ,cAAW;8BACnC;;;;;;;;;;;;;;;;;AAMb;AASO,SAAS,cAAc,EAAE,KAAK,EAAE,QAAQ,EAAE,SAAS,EAAE,UAAU,CAAC,EAAsB;IAC3F,MAAM,YAAY,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAC5B,MAAM,cAAc;QAClB,GAAG;QACH,GAAG;QACH,GAAG;IACL;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,aAAa;QAC3B,mBAAiB,GAAG,UAAU,MAAM,CAAC;;0BAErC,8OAAC;gBACC,IAAI,GAAG,UAAU,MAAM,CAAC;gBACxB,WAAU;0BAET;;;;;;0BAEH,8OAAC;gBACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,uEAAuE,WAAW,CAAC,QAAQ;gBACzG,MAAK;gBACL,mBAAiB,GAAG,UAAU,MAAM,CAAC;0BAEpC;;;;;;;;;;;;AAIT;AAQO,SAAS,YAAY,EAAE,KAAK,EAAE,KAAK,EAAE,SAAS,EAAoB;IACvE,MAAM,UAAU,CAAA,GAAA,qMAAA,CAAA,QAAW,AAAD;IAE1B,qBACE,8OAAC;QAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,wFAAwF;;0BACzG,8OAAC;gBACC,IAAI,GAAG,QAAQ,MAAM,CAAC;gBACtB,WAAU;0BAET;;;;;;0BAEH,8OAAC;gBACC,WAAU;gBACV,mBAAiB,GAAG,QAAQ,MAAM,CAAC;gBACnC,MAAK;0BAEJ,uBACC,8OAAC;oBAAK,WAAU;oBAA+B,cAAY,GAAG,MAAM,aAAa,CAAC;8BAAE;;;;;;;;;;;;;;;;;AAO9F", "debugId": null}}, {"offset": {"line": 2849, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/ui/modal.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport { XIcon } from \"lucide-react\"\nimport { cn } from \"@/lib/utils\"\n\ninterface ModalProps {\n  open: boolean\n  onOpenChange: (open: boolean) => void\n  children: React.ReactNode\n  className?: string\n}\n\ninterface ModalContentProps {\n  children: React.ReactNode\n  className?: string\n  showCloseButton?: boolean\n  onClose?: () => void\n}\n\ninterface ModalHeaderProps {\n  children: React.ReactNode\n  className?: string\n}\n\ninterface ModalFooterProps {\n  children: React.ReactNode\n  className?: string\n}\n\ninterface ModalTitleProps {\n  children: React.ReactNode\n  className?: string\n}\n\ninterface ModalDescriptionProps {\n  children: React.ReactNode\n  className?: string\n}\n\nfunction Modal({ open, onOpenChange, children, className }: ModalProps) {\n  React.useEffect(() => {\n    const handleEscape = (e: KeyboardEvent) => {\n      if (e.key === \"Escape\") {\n        onOpenChange(false)\n      }\n    }\n\n    if (open) {\n      document.addEventListener(\"keydown\", handleEscape)\n      document.body.style.overflow = \"hidden\"\n    }\n\n    return () => {\n      document.removeEventListener(\"keydown\", handleEscape)\n      document.body.style.overflow = \"unset\"\n    }\n  }, [open, onOpenChange])\n\n  if (!open) return null\n\n  return (\n    <div className=\"fixed inset-0 z-50 flex items-center justify-center\">\n      {/* Backdrop */}\n      <div\n        className=\"fixed inset-0 bg-black/50 backdrop-blur-sm\"\n        onClick={() => onOpenChange(false)}\n      />\n      {/* Modal Content */}\n      <div className={cn(\"relative z-10\", className)}>\n        {children}\n      </div>\n    </div>\n  )\n}\n\nfunction ModalContent({\n  children,\n  className,\n  showCloseButton = true,\n  onClose\n}: ModalContentProps) {\n  return (\n    <div\n      className={cn(\n        \"bg-background rounded-xl border border-border/50 shadow-2xl\",\n        \"backdrop-blur-sm bg-background/95 supports-[backdrop-filter]:bg-background/80\",\n        \"animate-in fade-in-0 zoom-in-95 slide-in-from-top-[2%] duration-300\",\n        \"max-h-[95vh] overflow-hidden flex flex-col\",\n        className\n      )}\n    >\n      {children}\n      {showCloseButton && onClose && (\n        <button\n          className=\"absolute top-4 right-4 rounded-xs opacity-70 transition-all duration-200 hover:opacity-100 hover:bg-accent hover:scale-110 focus:ring-2 focus:ring-ring focus:ring-offset-2 focus:outline-none disabled:pointer-events-none p-2\"\n          onClick={onClose}\n        >\n          <XIcon className=\"h-4 w-4\" />\n          <span className=\"sr-only\">Close</span>\n        </button>\n      )}\n    </div>\n  )\n}\n\nfunction ModalHeader({ children, className }: ModalHeaderProps) {\n  return (\n    <div\n      className={cn(\n        \"flex flex-col gap-3 text-center sm:text-left px-8 pt-6 pb-4\",\n        \"border-b border-border/50 bg-muted/20\",\n        className\n      )}\n    >\n      {children}\n    </div>\n  )\n}\n\nfunction ModalFooter({ children, className }: ModalFooterProps) {\n  return (\n    <div\n      className={cn(\n        \"flex flex-col-reverse gap-3 sm:flex-row sm:justify-end px-8 py-6\",\n        \"border-t border-border/50 bg-muted/10\",\n        className\n      )}\n    >\n      {children}\n    </div>\n  )\n}\n\nfunction ModalTitle({ children, className }: ModalTitleProps) {\n  return (\n    <h2\n      className={cn(\n        \"text-xl font-semibold leading-tight tracking-tight text-foreground\",\n        className\n      )}\n    >\n      {children}\n    </h2>\n  )\n}\n\nfunction ModalDescription({ children, className }: ModalDescriptionProps) {\n  return (\n    <p\n      className={cn(\n        \"text-muted-foreground text-sm leading-relaxed\",\n        className\n      )}\n    >\n      {children}\n    </p>\n  )\n}\n\nexport {\n  Modal,\n  ModalContent,\n  ModalHeader,\n  ModalFooter,\n  ModalTitle,\n  ModalDescription,\n}\n"], "names": [], "mappings": ";;;;;;;;;AAEA;AACA;AACA;AAJA;;;;;AAwCA,SAAS,MAAM,EAAE,IAAI,EAAE,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAc;IACpE,CAAA,GAAA,qMAAA,CAAA,YAAe,AAAD,EAAE;QACd,MAAM,eAAe,CAAC;YACpB,IAAI,EAAE,GAAG,KAAK,UAAU;gBACtB,aAAa;YACf;QACF;QAEA,IAAI,MAAM;YACR,SAAS,gBAAgB,CAAC,WAAW;YACrC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;QAEA,OAAO;YACL,SAAS,mBAAmB,CAAC,WAAW;YACxC,SAAS,IAAI,CAAC,KAAK,CAAC,QAAQ,GAAG;QACjC;IACF,GAAG;QAAC;QAAM;KAAa;IAEvB,IAAI,CAAC,MAAM,OAAO;IAElB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBACC,WAAU;gBACV,SAAS,IAAM,aAAa;;;;;;0BAG9B,8OAAC;gBAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iBAAiB;0BACjC;;;;;;;;;;;;AAIT;AAEA,SAAS,aAAa,EACpB,QAAQ,EACR,SAAS,EACT,kBAAkB,IAAI,EACtB,OAAO,EACW;IAClB,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA,iFACA,uEACA,8CACA;;YAGD;YACA,mBAAmB,yBAClB,8OAAC;gBACC,WAAU;gBACV,SAAS;;kCAET,8OAAC,gMAAA,CAAA,QAAK;wBAAC,WAAU;;;;;;kCACjB,8OAAC;wBAAK,WAAU;kCAAU;;;;;;;;;;;;;;;;;;AAKpC;AAEA,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAoB;IAC5D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,+DACA,yCACA;kBAGD;;;;;;AAGP;AAEA,SAAS,YAAY,EAAE,QAAQ,EAAE,SAAS,EAAoB;IAC5D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,oEACA,yCACA;kBAGD;;;;;;AAGP;AAEA,SAAS,WAAW,EAAE,QAAQ,EAAE,SAAS,EAAmB;IAC1D,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,sEACA;kBAGD;;;;;;AAGP;AAEA,SAAS,iBAAiB,EAAE,QAAQ,EAAE,SAAS,EAAyB;IACtE,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,iDACA;kBAGD;;;;;;AAGP", "debugId": null}}, {"offset": {"line": 2996, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/ui/secure-api-key-display.tsx"], "sourcesContent": ["\"use client\"\n\nimport React, { useState, useEffect, useRef } from 'react';\nimport { Button } from '@/components/ui/button';\nimport { Input } from '@/components/ui/input';\nimport { Copy, Eye, EyeOff, Shield } from 'lucide-react';\nimport { toast } from 'sonner';\nimport { useReAuth } from '@/contexts/re-auth-context';\nimport { useQuery } from 'convex/react';\nimport { api } from '../../../convex/_generated/api';\n\ninterface SecureApiKeyDisplayProps {\n  apiKey: string;\n  keyId: string;\n  apiKeyDocId?: string; // Convex document ID for fetching full key\n  className?: string;\n  showCopyButton?: boolean;\n  showToggleButton?: boolean;\n  placeholder?: string;\n  allowImmediateAccess?: boolean; // For newly created keys\n}\n\nexport function SecureApiKeyDisplay({\n  apiKey,\n  keyId,\n  apiKeyDocId,\n  className = \"\",\n  showCopyButton = true,\n  showToggleButton = true,\n  placeholder = \"••••••••••••••••••••••••••••••••\",\n  allowImmediateAccess = false\n}: SecureApiKeyDisplayProps) {\n  const [isVisible, setIsVisible] = useState(allowImmediateAccess);\n  const [isAuthenticating, setIsAuthenticating] = useState(false);\n  const [inputWidth, setInputWidth] = useState<string>('200px');\n  const { requestReAuth, isReAuthValid } = useReAuth();\n  const inputRef = useRef<HTMLInputElement>(null);\n\n  // Fetch full API key when needed (only when visible and we have a doc ID)\n  const fullApiKeyData = useQuery(\n    api.apiKeys?.getFullApiKeyById,\n    isVisible && apiKeyDocId ? { id: apiKeyDocId as any } : \"skip\"\n  );\n\n\n\n  const handleRevealKey = async () => {\n    if (isVisible) {\n      setIsVisible(false);\n      return;\n    }\n\n    // If immediate access is allowed, show the key without re-auth\n    if (allowImmediateAccess) {\n      console.log('Immediate access allowed, showing key');\n      setIsVisible(true);\n      return;\n    }\n\n    // Check if we need re-authentication\n    if (!isReAuthValid()) {\n      setIsAuthenticating(true);\n      try {\n        console.log('Requesting re-auth for key reveal');\n        const success = await requestReAuth();\n        console.log('Re-auth result:', success); // Debug log\n        if (success) {\n          console.log('Re-auth successful, setting visible to true');\n          setIsVisible(true);\n          toast.success('API key revealed');\n        } else {\n          toast.error('Authentication required to view API key');\n        }\n      } catch (error) {\n        console.error('Re-auth error:', error); // Debug log\n        toast.error('Authentication failed');\n      } finally {\n        setIsAuthenticating(false);\n      }\n    } else {\n      console.log('Re-auth still valid, setting visible to true');\n      setIsVisible(true);\n    }\n  };\n\n  const handleCopyKey = async () => {\n    // If immediate access is allowed, copy without re-auth\n    if (allowImmediateAccess) {\n      console.log('Immediate access allowed, copying key');\n      await copyToClipboard();\n      return;\n    }\n\n    // Check if we need re-authentication\n    if (!isReAuthValid()) {\n      setIsAuthenticating(true);\n      try {\n        console.log('Requesting re-auth for key copy');\n        const success = await requestReAuth();\n        if (success) {\n          console.log('Re-auth successful for copy, copying key');\n          await copyToClipboard();\n        } else {\n          toast.error('Authentication required to copy API key');\n        }\n      } catch (error) {\n        toast.error('Authentication failed');\n      } finally {\n        setIsAuthenticating(false);\n      }\n    } else {\n      await copyToClipboard();\n    }\n  };\n\n  const copyToClipboard = async () => {\n    try {\n      // Use the full API key if available, otherwise use the provided key\n      const keyToCopy = fullApiKeyData?.key || apiKey;\n      await navigator.clipboard.writeText(keyToCopy);\n      toast.success('API key copied to clipboard');\n    } catch (error) {\n      toast.error('Failed to copy API key');\n    }\n  };\n\n  // Use full API key if available and visible, otherwise use the provided (possibly masked) key\n  const actualApiKey = isVisible && fullApiKeyData?.key ? fullApiKeyData.key : apiKey;\n  const displayValue = isVisible ? actualApiKey : placeholder;\n\n  // Calculate optimal width based on the actual API key (not display value)\n  // This ensures consistent sizing regardless of visibility state\n  const calculateOptimalWidth = (apiKeyText: string): string => {\n    if (!apiKeyText) return '400px'; // Default width for placeholder\n\n    // More accurate character width for monospace font at text-xs\n    const charWidth = 7.4;\n    const padding = 16; // Minimal padding (8px left + 8px right)\n    const buttonSpace = 28; // Fixed space for consistency (accounts for shield icon)\n\n    // Calculate width based on the actual API key length (not display value)\n    const contentWidth = apiKeyText.length * charWidth;\n    const totalWidth = contentWidth + padding + buttonSpace;\n\n    // Set reasonable min and max widths with responsive behavior\n    const minWidth = 400; // Increased minimum for better appearance\n    const viewportWidth = typeof window !== 'undefined' ? window.innerWidth : 1200;\n    const maxWidth = Math.min(800, viewportWidth * 0.5);\n\n    const finalWidth = Math.max(minWidth, Math.min(maxWidth, totalWidth));\n    return `${finalWidth}px`;\n  };\n\n  // Update input width based on actual API key (not display value)\n  // This ensures consistent sizing regardless of visibility state\n  useEffect(() => {\n    const keyToMeasure = fullApiKeyData?.key || apiKey;\n    const newWidth = calculateOptimalWidth(keyToMeasure);\n    setInputWidth(newWidth);\n  }, [apiKey, fullApiKeyData?.key]);\n\n  // Handle window resize for responsive behavior\n  useEffect(() => {\n    const handleResize = () => {\n      const keyToMeasure = fullApiKeyData?.key || apiKey;\n      const newWidth = calculateOptimalWidth(keyToMeasure);\n      setInputWidth(newWidth);\n    };\n\n    if (typeof window !== 'undefined') {\n      window.addEventListener('resize', handleResize);\n      return () => window.removeEventListener('resize', handleResize);\n    }\n  }, [apiKey, fullApiKeyData?.key]);\n\n\n\n  return (\n    <div className={`flex items-center gap-2 ${className}`}>\n      <div className=\"relative\" style={{ width: inputWidth, minWidth: '200px', maxWidth: '100%' }}>\n        <Input\n          ref={inputRef}\n          value={displayValue}\n          readOnly\n          className=\"font-mono text-xs cursor-text w-full px-3 py-2 pr-8\"\n          placeholder={placeholder}\n          style={{\n            whiteSpace: 'nowrap',\n            textOverflow: 'clip'\n          }}\n          title={isVisible ? displayValue : \"Click the eye icon to reveal the API key\"}\n        />\n        {!isVisible && (\n          <div className=\"absolute inset-y-0 right-2 flex items-center\">\n            <Shield className=\"h-4 w-4 text-muted-foreground\" />\n          </div>\n        )}\n      </div>\n\n      <div className=\"flex gap-1 flex-shrink-0\">\n        {showToggleButton && (\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={handleRevealKey}\n            disabled={isAuthenticating}\n            title={isVisible ? \"Hide API key\" : \"Show API key (requires authentication)\"}\n          >\n            {isVisible ? (\n              <EyeOff className=\"h-4 w-4\" />\n            ) : (\n              <Eye className=\"h-4 w-4\" />\n            )}\n          </Button>\n        )}\n\n        {showCopyButton && (\n          <Button\n            variant=\"outline\"\n            size=\"sm\"\n            onClick={handleCopyKey}\n            disabled={isAuthenticating}\n            title=\"Copy API key (requires authentication)\"\n          >\n            <Copy className=\"h-4 w-4\" />\n          </Button>\n        )}\n      </div>\n    </div>\n  );\n}\n\ninterface SecureApiKeyFieldProps {\n  label: string;\n  apiKey: string;\n  keyId: string;\n  apiKeyDocId?: string;\n  description?: string;\n  className?: string;\n}\n\nexport function SecureApiKeyField({\n  label,\n  apiKey,\n  keyId,\n  apiKeyDocId,\n  description,\n  className = \"\"\n}: SecureApiKeyFieldProps) {\n  return (\n    <div className={`space-y-2 ${className}`}>\n      <div className=\"flex items-center justify-between\">\n        <label className=\"text-sm font-medium\">{label}</label>\n        <div className=\"flex items-center gap-1 text-xs text-muted-foreground\">\n          <Shield className=\"h-3 w-3\" />\n          <span>Protected</span>\n        </div>\n      </div>\n      \n      <SecureApiKeyDisplay\n        apiKey={apiKey}\n        keyId={keyId}\n        apiKeyDocId={apiKeyDocId}\n      />\n      \n      {description && (\n        <p className=\"text-xs text-muted-foreground\">{description}</p>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;;AAEA;AACA;AACA;AACA;AAAA;AAAA;AAAA;AACA;AACA;AACA;AAAA;AACA;AATA;;;;;;;;;;AAsBO,SAAS,oBAAoB,EAClC,MAAM,EACN,KAAK,EACL,WAAW,EACX,YAAY,EAAE,EACd,iBAAiB,IAAI,EACrB,mBAAmB,IAAI,EACvB,cAAc,kCAAkC,EAChD,uBAAuB,KAAK,EACH;IACzB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACrD,MAAM,EAAE,aAAa,EAAE,aAAa,EAAE,GAAG,CAAA,GAAA,yIAAA,CAAA,YAAS,AAAD;IACjD,MAAM,WAAW,CAAA,GAAA,qMAAA,CAAA,SAAM,AAAD,EAAoB;IAE1C,0EAA0E;IAC1E,MAAM,iBAAiB,CAAA,GAAA,wJAAA,CAAA,WAAQ,AAAD,EAC5B,2HAAA,CAAA,MAAG,CAAC,OAAO,EAAE,mBACb,aAAa,cAAc;QAAE,IAAI;IAAmB,IAAI;IAK1D,MAAM,kBAAkB;QACtB,IAAI,WAAW;YACb,aAAa;YACb;QACF;QAEA,+DAA+D;QAC/D,IAAI,sBAAsB;YACxB,QAAQ,GAAG,CAAC;YACZ,aAAa;YACb;QACF;QAEA,qCAAqC;QACrC,IAAI,CAAC,iBAAiB;YACpB,oBAAoB;YACpB,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,MAAM,UAAU,MAAM;gBACtB,QAAQ,GAAG,CAAC,mBAAmB,UAAU,YAAY;gBACrD,IAAI,SAAS;oBACX,QAAQ,GAAG,CAAC;oBACZ,aAAa;oBACb,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;gBAChB,OAAO;oBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,kBAAkB,QAAQ,YAAY;gBACpD,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,oBAAoB;YACtB;QACF,OAAO;YACL,QAAQ,GAAG,CAAC;YACZ,aAAa;QACf;IACF;IAEA,MAAM,gBAAgB;QACpB,uDAAuD;QACvD,IAAI,sBAAsB;YACxB,QAAQ,GAAG,CAAC;YACZ,MAAM;YACN;QACF;QAEA,qCAAqC;QACrC,IAAI,CAAC,iBAAiB;YACpB,oBAAoB;YACpB,IAAI;gBACF,QAAQ,GAAG,CAAC;gBACZ,MAAM,UAAU,MAAM;gBACtB,IAAI,SAAS;oBACX,QAAQ,GAAG,CAAC;oBACZ,MAAM;gBACR,OAAO;oBACL,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACd,SAAU;gBACR,oBAAoB;YACtB;QACF,OAAO;YACL,MAAM;QACR;IACF;IAEA,MAAM,kBAAkB;QACtB,IAAI;YACF,oEAAoE;YACpE,MAAM,YAAY,gBAAgB,OAAO;YACzC,MAAM,UAAU,SAAS,CAAC,SAAS,CAAC;YACpC,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;QAChB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;QACd;IACF;IAEA,8FAA8F;IAC9F,MAAM,eAAe,aAAa,gBAAgB,MAAM,eAAe,GAAG,GAAG;IAC7E,MAAM,eAAe,YAAY,eAAe;IAEhD,0EAA0E;IAC1E,gEAAgE;IAChE,MAAM,wBAAwB,CAAC;QAC7B,IAAI,CAAC,YAAY,OAAO,SAAS,gCAAgC;QAEjE,8DAA8D;QAC9D,MAAM,YAAY;QAClB,MAAM,UAAU,IAAI,yCAAyC;QAC7D,MAAM,cAAc,IAAI,yDAAyD;QAEjF,yEAAyE;QACzE,MAAM,eAAe,WAAW,MAAM,GAAG;QACzC,MAAM,aAAa,eAAe,UAAU;QAE5C,6DAA6D;QAC7D,MAAM,WAAW,KAAK,0CAA0C;QAChE,MAAM,gBAAgB,6EAAoD;QAC1E,MAAM,WAAW,KAAK,GAAG,CAAC,KAAK,gBAAgB;QAE/C,MAAM,aAAa,KAAK,GAAG,CAAC,UAAU,KAAK,GAAG,CAAC,UAAU;QACzD,OAAO,GAAG,WAAW,EAAE,CAAC;IAC1B;IAEA,iEAAiE;IACjE,gEAAgE;IAChE,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe,gBAAgB,OAAO;QAC5C,MAAM,WAAW,sBAAsB;QACvC,cAAc;IAChB,GAAG;QAAC;QAAQ,gBAAgB;KAAI;IAEhC,+CAA+C;IAC/C,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,eAAe;YACnB,MAAM,eAAe,gBAAgB,OAAO;YAC5C,MAAM,WAAW,sBAAsB;YACvC,cAAc;QAChB;QAEA,uCAAmC;;QAGnC;IACF,GAAG;QAAC;QAAQ,gBAAgB;KAAI;IAIhC,qBACE,8OAAC;QAAI,WAAW,CAAC,wBAAwB,EAAE,WAAW;;0BACpD,8OAAC;gBAAI,WAAU;gBAAW,OAAO;oBAAE,OAAO;oBAAY,UAAU;oBAAS,UAAU;gBAAO;;kCACxF,8OAAC,iIAAA,CAAA,QAAK;wBACJ,KAAK;wBACL,OAAO;wBACP,QAAQ;wBACR,WAAU;wBACV,aAAa;wBACb,OAAO;4BACL,YAAY;4BACZ,cAAc;wBAChB;wBACA,OAAO,YAAY,eAAe;;;;;;oBAEnC,CAAC,2BACA,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4BAAC,WAAU;;;;;;;;;;;;;;;;;0BAKxB,8OAAC;gBAAI,WAAU;;oBACZ,kCACC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU;wBACV,OAAO,YAAY,iBAAiB;kCAEnC,0BACC,8OAAC,0MAAA,CAAA,SAAM;4BAAC,WAAU;;;;;iDAElB,8OAAC,gMAAA,CAAA,MAAG;4BAAC,WAAU;;;;;;;;;;;oBAKpB,gCACC,8OAAC,kIAAA,CAAA,SAAM;wBACL,SAAQ;wBACR,MAAK;wBACL,SAAS;wBACT,UAAU;wBACV,OAAM;kCAEN,cAAA,8OAAC,kMAAA,CAAA,OAAI;4BAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;AAM5B;AAWO,SAAS,kBAAkB,EAChC,KAAK,EACL,MAAM,EACN,KAAK,EACL,WAAW,EACX,WAAW,EACX,YAAY,EAAE,EACS;IACvB,qBACE,8OAAC;QAAI,WAAW,CAAC,UAAU,EAAE,WAAW;;0BACtC,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAM,WAAU;kCAAuB;;;;;;kCACxC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,sMAAA,CAAA,SAAM;gCAAC,WAAU;;;;;;0CAClB,8OAAC;0CAAK;;;;;;;;;;;;;;;;;;0BAIV,8OAAC;gBACC,QAAQ;gBACR,OAAO;gBACP,aAAa;;;;;;YAGd,6BACC,8OAAC;gBAAE,WAAU;0BAAiC;;;;;;;;;;;;AAItD", "debugId": null}}, {"offset": {"line": 3331, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/components/ui/tooltip.tsx"], "sourcesContent": ["\"use client\"\n\nimport * as React from \"react\"\nimport * as TooltipPrimitive from \"@radix-ui/react-tooltip\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction TooltipProvider({\n  delayDuration = 0,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Provider>) {\n  return (\n    <TooltipPrimitive.Provider\n      data-slot=\"tooltip-provider\"\n      delayDuration={delayDuration}\n      {...props}\n    />\n  )\n}\n\nfunction Tooltip({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Root>) {\n  return (\n    <TooltipProvider>\n      <TooltipPrimitive.Root data-slot=\"tooltip\" {...props} />\n    </TooltipProvider>\n  )\n}\n\nfunction TooltipTrigger({\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Trigger>) {\n  return <TooltipPrimitive.Trigger data-slot=\"tooltip-trigger\" {...props} />\n}\n\nfunction TooltipContent({\n  className,\n  sideOffset = 0,\n  children,\n  ...props\n}: React.ComponentProps<typeof TooltipPrimitive.Content>) {\n  return (\n    <TooltipPrimitive.Portal>\n      <TooltipPrimitive.Content\n        data-slot=\"tooltip-content\"\n        sideOffset={sideOffset}\n        className={cn(\n          \"bg-primary text-primary-foreground animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 w-fit origin-(--radix-tooltip-content-transform-origin) rounded-md px-3 py-1.5 text-xs text-balance\",\n          className\n        )}\n        {...props}\n      >\n        {children}\n        <TooltipPrimitive.Arrow className=\"bg-primary fill-primary z-50 size-2.5 translate-y-[calc(-50%_-_2px)] rotate-45 rounded-[2px]\" />\n      </TooltipPrimitive.Content>\n    </TooltipPrimitive.Portal>\n  )\n}\n\nexport { Tooltip, TooltipTrigger, TooltipContent, TooltipProvider }\n"], "names": [], "mappings": ";;;;;;;AAGA;AAEA;AALA;;;;AAOA,SAAS,gBAAgB,EACvB,gBAAgB,CAAC,EACjB,GAAG,OACoD;IACvD,qBACE,8OAAC,mKAAA,CAAA,WAAyB;QACxB,aAAU;QACV,eAAe;QACd,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,QAAQ,EACf,GAAG,OACgD;IACnD,qBACE,8OAAC;kBACC,cAAA,8OAAC,mKAAA,CAAA,OAAqB;YAAC,aAAU;YAAW,GAAG,KAAK;;;;;;;;;;;AAG1D;AAEA,SAAS,eAAe,EACtB,GAAG,OACmD;IACtD,qBAAO,8OAAC,mKAAA,CAAA,UAAwB;QAAC,aAAU;QAAmB,GAAG,KAAK;;;;;;AACxE;AAEA,SAAS,eAAe,EACtB,SAAS,EACT,aAAa,CAAC,EACd,QAAQ,EACR,GAAG,OACmD;IACtD,qBACE,8OAAC,mKAAA,CAAA,SAAuB;kBACtB,cAAA,8OAAC,mKAAA,CAAA,UAAwB;YACvB,aAAU;YACV,YAAY;YACZ,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,0aACA;YAED,GAAG,KAAK;;gBAER;8BACD,8OAAC,mKAAA,CAAA,QAAsB;oBAAC,WAAU;;;;;;;;;;;;;;;;;AAI1C", "debugId": null}}, {"offset": {"line": 3416, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/lib/graphql/queries.ts"], "sourcesContent": ["import { gql } from '@apollo/client';\n\n// Fragment for API Key fields\nexport const API_KEY_FRAGMENT = gql`\n  fragment ApiKeyFields on ApiKey {\n    id\n    name\n    key\n    keyId\n    environment\n    permissions\n    isActive\n    expiresAt\n    lastUsedAt\n    usageCount\n    rateLimit {\n      requestsPerMinute\n      requestsPerHour\n      requestsPerDay\n      burstLimit\n    }\n    revokedAt\n    revokedBy\n    revocationReason\n    rotatedAt\n    rotatedBy\n    rotationReason\n    createdBy\n    createdAt\n    updatedAt\n  }\n`;\n\n// Query to get all API keys\nexport const GET_API_KEYS = gql`\n  query GetApiKeys($input: GetApiKeysInput) {\n    getApiKeys(input: $input) {\n      ...ApiKeyFields\n    }\n  }\n  ${API_KEY_FRAGMENT}\n`;\n\n// Query to get API key by ID\nexport const GET_API_KEY_BY_ID = gql`\n  query GetApiKeyById($id: ID!) {\n    getApiKeyById(id: $id) {\n      ...ApiKeyFields\n    }\n  }\n  ${API_KEY_FRAGMENT}\n`;\n\n// Query to get full API key by ID (for authenticated access)\nexport const GET_FULL_API_KEY_BY_ID = gql`\n  query GetFullApiKeyById($id: ID!) {\n    getFullApiKeyById(id: $id) {\n      ...ApiKeyFields\n    }\n  }\n  ${API_KEY_FRAGMENT}\n`;\n\n// Query to get API key statistics\nexport const GET_API_KEY_STATS = gql`\n  query GetApiKeyStats {\n    getApiKeyStats {\n      total\n      active\n      inactive\n      expired\n      recentlyUsed\n      totalUsage\n    }\n  }\n`;\n\n// Query to validate API key\nexport const VALIDATE_API_KEY = gql`\n  query ValidateApiKey($key: String!) {\n    validateApiKey(key: $key) {\n      ...ApiKeyFields\n    }\n  }\n  ${API_KEY_FRAGMENT}\n`;\n"], "names": [], "mappings": ";;;;;;;;AAAA;;AAGO,MAAM,mBAAmB,8IAAA,CAAA,MAAG,CAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4BpC,CAAC;AAGM,MAAM,eAAe,8IAAA,CAAA,MAAG,CAAC;;;;;;EAM9B,EAAE,iBAAiB;AACrB,CAAC;AAGM,MAAM,oBAAoB,8IAAA,CAAA,MAAG,CAAC;;;;;;EAMnC,EAAE,iBAAiB;AACrB,CAAC;AAGM,MAAM,yBAAyB,8IAAA,CAAA,MAAG,CAAC;;;;;;EAMxC,EAAE,iBAAiB;AACrB,CAAC;AAGM,MAAM,oBAAoB,8IAAA,CAAA,MAAG,CAAC;;;;;;;;;;;AAWrC,CAAC;AAGM,MAAM,mBAAmB,8IAAA,CAAA,MAAG,CAAC;;;;;;EAMlC,EAAE,iBAAiB;AACrB,CAAC", "debugId": null}}, {"offset": {"line": 3505, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/lib/graphql/mutations.ts"], "sourcesContent": ["import { gql } from '@apollo/client';\nimport { API_KEY_FRAGMENT } from './queries';\n\n// Mutation to create a new API key\nexport const CREATE_API_KEY = gql`\n  mutation CreateApiKey($input: CreateApiKeyInput!) {\n    createApiKey(input: $input) {\n      ...ApiKeyFields\n    }\n  }\n  ${API_KEY_FRAGMENT}\n`;\n\n// Mutation to update an API key\nexport const UPDATE_API_KEY = gql`\n  mutation UpdateApiKey($input: UpdateApiKeyInput!) {\n    updateApiKey(input: $input) {\n      ...ApiKeyFields\n    }\n  }\n  ${API_KEY_FRAGMENT}\n`;\n\n// Mutation to revoke an API key\nexport const REVOKE_API_KEY = gql`\n  mutation RevokeApiKey($input: RevokeApiKeyInput!) {\n    revokeApiKey(input: $input) {\n      ...ApiKeyFields\n    }\n  }\n  ${API_KEY_FRAGMENT}\n`;\n\n// Mutation to delete an API key\nexport const DELETE_API_KEY = gql`\n  mutation DeleteApiKey($input: DeleteApiKeyInput!) {\n    deleteApiKey(input: $input)\n  }\n`;\n"], "names": [], "mappings": ";;;;;;AAAA;AACA;;;AAGO,MAAM,iBAAiB,8IAAA,CAAA,MAAG,CAAC;;;;;;EAMhC,EAAE,gIAAA,CAAA,mBAAgB,CAAC;AACrB,CAAC;AAGM,MAAM,iBAAiB,8IAAA,CAAA,MAAG,CAAC;;;;;;EAMhC,EAAE,gIAAA,CAAA,mBAAgB,CAAC;AACrB,CAAC;AAGM,MAAM,iBAAiB,8IAAA,CAAA,MAAG,CAAC;;;;;;EAMhC,EAAE,gIAAA,CAAA,mBAAgB,CAAC;AACrB,CAAC;AAGM,MAAM,iBAAiB,8IAAA,CAAA,MAAG,CAAC;;;;AAIlC,CAAC", "debugId": null}}, {"offset": {"line": 3550, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/hooks/useApiKeysGraphQL.ts"], "sourcesContent": ["import { useQuery, useMutation, useApolloClient } from '@apollo/client';\nimport {\n  GET_API_KEYS,\n  GET_API_KEY_BY_ID,\n  GET_FULL_API_KEY_BY_ID,\n  GET_API_KEY_STATS,\n  VALIDATE_API_KEY,\n} from '@/lib/graphql/queries';\nimport {\n  CREATE_API_KEY,\n  UPDATE_API_KEY,\n  REVOKE_API_KEY,\n  DELETE_API_KEY,\n} from '@/lib/graphql/mutations';\n\n// Types for inputs\ninterface GetApiKeysInput {\n  search?: string;\n  isActive?: boolean;\n  createdBy?: string;\n  limit?: number;\n  offset?: number;\n}\n\ninterface CreateApiKeyInput {\n  name: string;\n  permissions: string[];\n  adminId: string;\n  environment?: 'live' | 'test';\n  expiresAt?: number;\n  rateLimit?: {\n    requestsPerMinute: number;\n    requestsPerHour: number;\n    requestsPerDay: number;\n    burstLimit?: number;\n  };\n}\n\ninterface UpdateApiKeyInput {\n  apiKeyId: string;\n  name?: string;\n  permissions?: string[];\n  isActive?: boolean;\n  expiresAt?: number;\n  rateLimit?: {\n    requestsPerMinute: number;\n    requestsPerHour: number;\n    requestsPerDay: number;\n    burstLimit?: number;\n  };\n  updatedBy: string;\n}\n\ninterface RevokeApiKeyInput {\n  apiKeyId: string;\n  revokedBy: string;\n  reason?: string;\n}\n\ninterface DeleteApiKeyInput {\n  apiKeyId: string;\n  deletedBy: string;\n  reason?: string;\n}\n\n// Custom hooks\nexport function useApiKeys(input?: GetApiKeysInput) {\n  const { data, loading, error, refetch } = useQuery(GET_API_KEYS, {\n    variables: { input },\n    errorPolicy: 'all',\n  });\n\n  return {\n    data: data?.getApiKeys || [],\n    loading,\n    error,\n    refetch,\n  };\n}\n\nexport function useApiKeyById(id: string) {\n  const { data, loading, error } = useQuery(GET_API_KEY_BY_ID, {\n    variables: { id },\n    skip: !id,\n    errorPolicy: 'all',\n  });\n\n  return {\n    data: data?.getApiKeyById,\n    loading,\n    error,\n  };\n}\n\nexport function useFullApiKeyById(id: string) {\n  const { data, loading, error } = useQuery(GET_FULL_API_KEY_BY_ID, {\n    variables: { id },\n    skip: !id,\n    errorPolicy: 'all',\n  });\n\n  return {\n    data: data?.getFullApiKeyById,\n    loading,\n    error,\n  };\n}\n\nexport function useApiKeyStats() {\n  const { data, loading, error, refetch } = useQuery(GET_API_KEY_STATS, {\n    errorPolicy: 'all',\n  });\n\n  return {\n    data: data?.getApiKeyStats,\n    loading,\n    error,\n    refetch,\n  };\n}\n\nexport function useValidateApiKey(key: string) {\n  const { data, loading, error } = useQuery(VALIDATE_API_KEY, {\n    variables: { key },\n    skip: !key,\n    errorPolicy: 'all',\n  });\n\n  return {\n    data: data?.validateApiKey,\n    loading,\n    error,\n  };\n}\n\n// Mutation hooks\nexport function useCreateApiKey() {\n  const client = useApolloClient();\n  const [createApiKeyMutation, { loading, error }] = useMutation(CREATE_API_KEY, {\n    onCompleted: () => {\n      // Refetch API keys list after creation\n      client.refetchQueries({\n        include: [GET_API_KEYS, GET_API_KEY_STATS],\n      });\n    },\n  });\n\n  const createApiKey = async (input: CreateApiKeyInput) => {\n    const result = await createApiKeyMutation({\n      variables: { input },\n    });\n    return result.data?.createApiKey;\n  };\n\n  return {\n    createApiKey,\n    loading,\n    error,\n  };\n}\n\nexport function useUpdateApiKey() {\n  const client = useApolloClient();\n  const [updateApiKeyMutation, { loading, error }] = useMutation(UPDATE_API_KEY, {\n    onCompleted: () => {\n      // Refetch API keys list after update\n      client.refetchQueries({\n        include: [GET_API_KEYS, GET_API_KEY_STATS],\n      });\n    },\n  });\n\n  const updateApiKey = async (input: UpdateApiKeyInput) => {\n    const result = await updateApiKeyMutation({\n      variables: { input },\n    });\n    return result.data?.updateApiKey;\n  };\n\n  return {\n    updateApiKey,\n    loading,\n    error,\n  };\n}\n\nexport function useRevokeApiKey() {\n  const client = useApolloClient();\n  const [revokeApiKeyMutation, { loading, error }] = useMutation(REVOKE_API_KEY, {\n    onCompleted: () => {\n      // Refetch API keys list after revocation\n      client.refetchQueries({\n        include: [GET_API_KEYS, GET_API_KEY_STATS],\n      });\n    },\n  });\n\n  const revokeApiKey = async (input: RevokeApiKeyInput) => {\n    const result = await revokeApiKeyMutation({\n      variables: { input },\n    });\n    return result.data?.revokeApiKey;\n  };\n\n  return {\n    revokeApiKey,\n    loading,\n    error,\n  };\n}\n\nexport function useDeleteApiKey() {\n  const client = useApolloClient();\n  const [deleteApiKeyMutation, { loading, error }] = useMutation(DELETE_API_KEY, {\n    onCompleted: () => {\n      // Refetch API keys list after deletion\n      client.refetchQueries({\n        include: [GET_API_KEYS, GET_API_KEY_STATS],\n      });\n    },\n  });\n\n  const deleteApiKey = async (input: DeleteApiKeyInput) => {\n    const result = await deleteApiKeyMutation({\n      variables: { input },\n    });\n    return result.data?.deleteApiKey;\n  };\n\n  return {\n    deleteApiKey,\n    loading,\n    error,\n  };\n}\n"], "names": [], "mappings": ";;;;;;;;;;;AAAA;AAAA;AAAA;AACA;AAOA;;;;AA0DO,SAAS,WAAW,KAAuB;IAChD,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,WAAQ,AAAD,EAAE,gIAAA,CAAA,eAAY,EAAE;QAC/D,WAAW;YAAE;QAAM;QACnB,aAAa;IACf;IAEA,OAAO;QACL,MAAM,MAAM,cAAc,EAAE;QAC5B;QACA;QACA;IACF;AACF;AAEO,SAAS,cAAc,EAAU;IACtC,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,WAAQ,AAAD,EAAE,gIAAA,CAAA,oBAAiB,EAAE;QAC3D,WAAW;YAAE;QAAG;QAChB,MAAM,CAAC;QACP,aAAa;IACf;IAEA,OAAO;QACL,MAAM,MAAM;QACZ;QACA;IACF;AACF;AAEO,SAAS,kBAAkB,EAAU;IAC1C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,WAAQ,AAAD,EAAE,gIAAA,CAAA,yBAAsB,EAAE;QAChE,WAAW;YAAE;QAAG;QAChB,MAAM,CAAC;QACP,aAAa;IACf;IAEA,OAAO;QACL,MAAM,MAAM;QACZ;QACA;IACF;AACF;AAEO,SAAS;IACd,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,OAAO,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,WAAQ,AAAD,EAAE,gIAAA,CAAA,oBAAiB,EAAE;QACpE,aAAa;IACf;IAEA,OAAO;QACL,MAAM,MAAM;QACZ;QACA;QACA;IACF;AACF;AAEO,SAAS,kBAAkB,GAAW;IAC3C,MAAM,EAAE,IAAI,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gKAAA,CAAA,WAAQ,AAAD,EAAE,gIAAA,CAAA,mBAAgB,EAAE;QAC1D,WAAW;YAAE;QAAI;QACjB,MAAM,CAAC;QACP,aAAa;IACf;IAEA,OAAO;QACL,MAAM,MAAM;QACZ;QACA;IACF;AACF;AAGO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,uKAAA,CAAA,kBAAe,AAAD;IAC7B,MAAM,CAAC,sBAAsB,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,GAAG,CAAA,GAAA,mKAAA,CAAA,cAAW,AAAD,EAAE,kIAAA,CAAA,iBAAc,EAAE;QAC7E,aAAa;YACX,uCAAuC;YACvC,OAAO,cAAc,CAAC;gBACpB,SAAS;oBAAC,gIAAA,CAAA,eAAY;oBAAE,gIAAA,CAAA,oBAAiB;iBAAC;YAC5C;QACF;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,SAAS,MAAM,qBAAqB;YACxC,WAAW;gBAAE;YAAM;QACrB;QACA,OAAO,OAAO,IAAI,EAAE;IACtB;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF;AAEO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,uKAAA,CAAA,kBAAe,AAAD;IAC7B,MAAM,CAAC,sBAAsB,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,GAAG,CAAA,GAAA,mKAAA,CAAA,cAAW,AAAD,EAAE,kIAAA,CAAA,iBAAc,EAAE;QAC7E,aAAa;YACX,qCAAqC;YACrC,OAAO,cAAc,CAAC;gBACpB,SAAS;oBAAC,gIAAA,CAAA,eAAY;oBAAE,gIAAA,CAAA,oBAAiB;iBAAC;YAC5C;QACF;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,SAAS,MAAM,qBAAqB;YACxC,WAAW;gBAAE;YAAM;QACrB;QACA,OAAO,OAAO,IAAI,EAAE;IACtB;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF;AAEO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,uKAAA,CAAA,kBAAe,AAAD;IAC7B,MAAM,CAAC,sBAAsB,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,GAAG,CAAA,GAAA,mKAAA,CAAA,cAAW,AAAD,EAAE,kIAAA,CAAA,iBAAc,EAAE;QAC7E,aAAa;YACX,yCAAyC;YACzC,OAAO,cAAc,CAAC;gBACpB,SAAS;oBAAC,gIAAA,CAAA,eAAY;oBAAE,gIAAA,CAAA,oBAAiB;iBAAC;YAC5C;QACF;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,SAAS,MAAM,qBAAqB;YACxC,WAAW;gBAAE;YAAM;QACrB;QACA,OAAO,OAAO,IAAI,EAAE;IACtB;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF;AAEO,SAAS;IACd,MAAM,SAAS,CAAA,GAAA,uKAAA,CAAA,kBAAe,AAAD;IAC7B,MAAM,CAAC,sBAAsB,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,GAAG,CAAA,GAAA,mKAAA,CAAA,cAAW,AAAD,EAAE,kIAAA,CAAA,iBAAc,EAAE;QAC7E,aAAa;YACX,uCAAuC;YACvC,OAAO,cAAc,CAAC;gBACpB,SAAS;oBAAC,gIAAA,CAAA,eAAY;oBAAE,gIAAA,CAAA,oBAAiB;iBAAC;YAC5C;QACF;IACF;IAEA,MAAM,eAAe,OAAO;QAC1B,MAAM,SAAS,MAAM,qBAAqB;YACxC,WAAW;gBAAE;YAAM;QACrB;QACA,OAAO,OAAO,IAAI,EAAE;IACtB;IAEA,OAAO;QACL;QACA;QACA;IACF;AACF", "debugId": null}}, {"offset": {"line": 3750, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/Benzochem%20Industries%20v2/edit-2/admin/src/app/dashboard/api-keys/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState } from \"react\";\nimport { ProtectedRoute } from \"@/components/auth/protected-route\";\nimport { DashboardLayout } from \"@/components/layout/dashboard-layout\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Button } from \"@/components/ui/button\";\nimport { Input } from \"@/components/ui/input\";\nimport { Label } from \"@/components/ui/label\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { \n  Table, \n  TableBody, \n  TableCell, \n  TableHead, \n  TableHeader, \n  TableRow \n} from \"@/components/ui/table\";\nimport {\n  DropdownMenu,\n  DropdownMenuContent,\n  DropdownMenuItem,\n  DropdownMenuLabel,\n  DropdownMenuSeparator,\n  DropdownMenuTrigger,\n} from \"@/components/ui/dropdown-menu\";\nimport {\n  Select,\n  SelectContent,\n  SelectItem,\n  SelectTrigger,\n  SelectValue,\n} from \"@/components/ui/select\";\n\nimport {\n  ConfirmDialog,\n  FormSection,\n  FormField,\n  DetailDialog,\n  DetailSection,\n  DetailField,\n} from \"@/components/ui/enhanced-dialog\";\nimport {\n  Modal,\n  ModalContent,\n  ModalDescription,\n  ModalFooter,\n  ModalHeader,\n  ModalTitle,\n} from \"@/components/ui/modal\";\nimport { SecureApiKeyDisplay, SecureApiKeyField } from \"@/components/ui/secure-api-key-display\";\nimport {\n  Search,\n  MoreHorizontal,\n  Eye,\n  Key,\n  Plus,\n  Trash2,\n  Shield,\n  Activity,\n  RefreshCw,\n  Download,\n  AlertTriangle,\n  Loader2,\n  Info\n} from \"lucide-react\";\nimport { Tooltip, TooltipContent, TooltipTrigger } from \"@/components/ui/tooltip\";\n\nimport { useMutation } from \"convex/react\";\nimport { api } from \"../../../../convex/_generated/api\";\nimport { useAuth } from \"@/contexts/auth-context\";\nimport { toast } from \"sonner\";\nimport {\n  useApiKeys,\n  useApiKeyStats,\n  useCreateApiKey,\n  useRevokeApiKey,\n  useDeleteApiKey,\n} from \"@/hooks/useApiKeysGraphQL\";\n\nexport default function APIKeysPage() {\n  const [searchTerm, setSearchTerm] = useState(\"\");\n  const [statusFilter, setStatusFilter] = useState<string>(\"all\");\n  const [currentPage, setCurrentPage] = useState(0);\n  const [selectedApiKey, setSelectedApiKey] = useState<any>(null);\n  const [showDetailsDialog, setShowDetailsDialog] = useState(false);\n  const [showCreateDialog, setShowCreateDialog] = useState(false);\n  const [newKeyName, setNewKeyName] = useState(\"\");\n  const [newKeyPermissions, setNewKeyPermissions] = useState<string[]>([]);\n  const [selectAllEssential, setSelectAllEssential] = useState(false);\n  const [showRevokeConfirm, setShowRevokeConfirm] = useState(false);\n  const [apiKeyToRevoke, setApiKeyToRevoke] = useState<string | null>(null);\n  const [apiKeyToDelete, setApiKeyToDelete] = useState<string | null>(null);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n  const [recentlyCreatedKeys, setRecentlyCreatedKeys] = useState<Set<string>>(new Set());\n  const pageSize = 20;\n\n  const { admin } = useAuth();\n\n  // GraphQL Queries\n  const { data: apiKeys, loading: apiKeysLoading, error: apiKeysError } = useApiKeys({\n    search: searchTerm || undefined,\n    isActive: statusFilter === \"all\" ? undefined : statusFilter === \"active\",\n    limit: pageSize,\n    offset: currentPage * pageSize,\n  });\n\n  const { data: apiKeyStats, loading: statsLoading, error: statsError } = useApiKeyStats();\n\n  // GraphQL Mutations\n  const { createApiKey, loading: isCreatingKey, error: createError } = useCreateApiKey();\n  const { revokeApiKey, loading: isRevoking, error: revokeError } = useRevokeApiKey();\n  const { deleteApiKey, loading: isDeleting, error: deleteError } = useDeleteApiKey();\n\n  // Keep Convex mutation for admin operations (until migrated)\n  const getOrCreateDemoAdmin = useMutation(api.admins.getOrCreateDemoAdmin);\n\n  const handleCreateApiKey = async () => {\n    if (!admin || !newKeyName.trim()) return;\n\n    try {\n      const adminId = await getOrCreateDemoAdmin({ email: admin.email });\n      const result = await createApiKey({\n        name: newKeyName,\n        permissions: newKeyPermissions,\n        adminId: adminId,\n      });\n\n      // Track this key as recently created for immediate access\n      if (result?.id) {\n        setRecentlyCreatedKeys(prev => new Set(prev).add(result.id));\n        // Remove from recently created after 5 minutes\n        setTimeout(() => {\n          setRecentlyCreatedKeys(prev => {\n            const newSet = new Set(prev);\n            newSet.delete(result.id);\n            return newSet;\n          });\n        }, 5 * 60 * 1000);\n      }\n\n      toast.success(\"API key created successfully\");\n      setShowCreateDialog(false);\n      setNewKeyName(\"\");\n      setNewKeyPermissions([]);\n      setSelectAllEssential(false);\n    } catch (error) {\n      toast.error(\"Failed to create API key\");\n      console.error(error);\n    }\n  };\n\n  const handleRevokeApiKey = async (apiKeyId: string) => {\n    setApiKeyToRevoke(apiKeyId);\n    setShowRevokeConfirm(true);\n  };\n\n  const confirmRevokeApiKey = async () => {\n    if (!admin || !apiKeyToRevoke) return;\n\n    try {\n      // Get or create admin ID first\n      const adminId = await getOrCreateDemoAdmin({ email: admin.email });\n\n      await revokeApiKey({\n        apiKeyId: apiKeyToRevoke,\n        revokedBy: adminId,\n        reason: \"Revoked via admin dashboard\"\n      });\n      toast.success(\"API key revoked successfully\");\n      setShowRevokeConfirm(false);\n      setApiKeyToRevoke(null);\n      if (selectedApiKey?.id === apiKeyToRevoke) {\n        setShowDetailsDialog(false);\n      }\n    } catch (error) {\n      toast.error(\"Failed to revoke API key\");\n      console.error(error);\n    }\n  };\n\n  const handleDeleteApiKey = async (apiKeyId: string) => {\n    setApiKeyToDelete(apiKeyId);\n    setShowDeleteConfirm(true);\n  };\n\n  const confirmDeleteApiKey = async () => {\n    if (!admin || !apiKeyToDelete) return;\n\n    try {\n      // Get or create admin ID first\n      const adminId = await getOrCreateDemoAdmin({ email: admin.email });\n\n      await deleteApiKey({\n        apiKeyId: apiKeyToDelete,\n        deletedBy: adminId,\n        reason: \"Permanently deleted via admin dashboard\"\n      });\n      toast.success(\"API key permanently deleted\");\n      setShowDeleteConfirm(false);\n      setApiKeyToDelete(null);\n      if (selectedApiKey?.id === apiKeyToDelete) {\n        setShowDetailsDialog(false);\n      }\n    } catch (error) {\n      toast.error(\"Failed to delete API key\");\n      console.error(error);\n    }\n  };\n\n\n\n  const formatDate = (timestamp: number) => {\n    return new Date(timestamp).toLocaleDateString(\"en-US\", {\n      year: \"numeric\",\n      month: \"short\",\n      day: \"numeric\",\n      hour: \"2-digit\",\n      minute: \"2-digit\",\n    });\n  };\n\n  const openDetailsDialog = (apiKey: any) => {\n    setSelectedApiKey(apiKey);\n    setShowDetailsDialog(true);\n  };\n\n\n\n  // Permission categories for better UX\n  const permissionCategories = {\n    essential: {\n      title: \"Essential Permissions\",\n      description: \"Core permissions typically required for frontend applications and basic API access\",\n      icon: Shield,\n      permissions: [\n        {\n          name: \"collections.read\",\n          label: \"Collections Read\",\n          description: \"View product collections and categories\"\n        },\n        {\n          name: \"collections.write\",\n          label: \"Collections Write\",\n          description: \"Create and modify product collections\"\n        },\n        {\n          name: \"products.read\",\n          label: \"Products Read\",\n          description: \"View product information and inventory\"\n        },\n        {\n          name: \"products.write\",\n          label: \"Products Write\",\n          description: \"Create and update product data\"\n        },\n        {\n          name: \"analytics.read\",\n          label: \"Analytics Read\",\n          description: \"Access basic analytics and reporting data\"\n        }\n      ]\n    },\n    advanced: {\n      title: \"Advanced/Optional Permissions\",\n      description: \"Additional permissions for specific features and administrative tasks\",\n      icon: Activity,\n      permissions: [\n        {\n          name: \"products.delete\",\n          label: \"Products Delete\",\n          description: \"Permanently remove products from the system\"\n        },\n        {\n          name: \"collections.delete\",\n          label: \"Collections Delete\",\n          description: \"Permanently remove collections\"\n        },\n        {\n          name: \"webhooks.read\",\n          label: \"Webhooks Read\",\n          description: \"View webhook configurations and logs\"\n        },\n        {\n          name: \"webhooks.write\",\n          label: \"Webhooks Write\",\n          description: \"Create and manage webhook endpoints\"\n        },\n        {\n          name: \"webhooks.delete\",\n          label: \"Webhooks Delete\",\n          description: \"Remove webhook configurations\"\n        },\n        {\n          name: \"users.read\",\n          label: \"Users Read\",\n          description: \"View user accounts and profiles\"\n        },\n        {\n          name: \"users.write\",\n          label: \"Users Write\",\n          description: \"Create and modify user accounts\"\n        },\n        {\n          name: \"users.approve\",\n          label: \"Users Approve\",\n          description: \"Approve or reject user registrations\"\n        },\n        {\n          name: \"orders.read\",\n          label: \"Orders Read\",\n          description: \"View order information and history\"\n        },\n        {\n          name: \"orders.write\",\n          label: \"Orders Write\",\n          description: \"Create and modify orders\"\n        },\n        {\n          name: \"orders.fulfill\",\n          label: \"Orders Fulfill\",\n          description: \"Process and fulfill customer orders\"\n        },\n        {\n          name: \"reports.read\",\n          label: \"Reports Read\",\n          description: \"Access detailed reports and analytics\"\n        },\n        {\n          name: \"settings.read\",\n          label: \"Settings Read\",\n          description: \"View system configuration and settings\"\n        }\n      ]\n    }\n  };\n\n  // Helper functions for permission management\n  const getEssentialPermissions = () => permissionCategories.essential.permissions.map(p => p.name);\n\n  const handleSelectAllEssential = (checked: boolean) => {\n    setSelectAllEssential(checked);\n    const essentialPerms = getEssentialPermissions();\n\n    if (checked) {\n      // Add all essential permissions that aren't already selected\n      const newPermissions = [...new Set([...newKeyPermissions, ...essentialPerms])];\n      setNewKeyPermissions(newPermissions);\n    } else {\n      // Remove all essential permissions\n      const filteredPermissions = newKeyPermissions.filter(p => !essentialPerms.includes(p));\n      setNewKeyPermissions(filteredPermissions);\n    }\n  };\n\n  const handlePermissionChange = (permissionName: string, checked: boolean) => {\n    if (checked) {\n      setNewKeyPermissions([...newKeyPermissions, permissionName]);\n    } else {\n      setNewKeyPermissions(newKeyPermissions.filter(p => p !== permissionName));\n    }\n\n    // Update \"Select All Essential\" state\n    const essentialPerms = getEssentialPermissions();\n    const hasAllEssential = essentialPerms.every(p =>\n      checked && permissionName === p ? true : newKeyPermissions.includes(p)\n    );\n    setSelectAllEssential(hasAllEssential);\n  };\n\n  const resetCreateForm = () => {\n    setNewKeyName(\"\");\n    setNewKeyPermissions([]);\n    setSelectAllEssential(false);\n  };\n\n  return (\n    <ProtectedRoute requiredPermission=\"api_keys.read\">\n      <DashboardLayout>\n        <div className=\"space-y-6\">\n          {/* Header */}\n          <div className=\"flex items-center justify-between\">\n            <div>\n              <h1 className=\"text-3xl font-bold tracking-tight\">API Keys</h1>\n              <p className=\"text-muted-foreground\">\n                Manage API keys for external integrations and access control\n              </p>\n            </div>\n            <div className=\"flex items-center gap-2\">\n              <Button variant=\"outline\" size=\"sm\">\n                <Download className=\"h-4 w-4 mr-2\" />\n                Export Usage\n              </Button>\n              <Button variant=\"outline\" size=\"sm\">\n                <RefreshCw className=\"h-4 w-4 mr-2\" />\n                Refresh\n              </Button>\n              <Button size=\"sm\" onClick={() => setShowCreateDialog(true)}>\n                <Plus className=\"h-4 w-4 mr-2\" />\n                Create API Key\n              </Button>\n\n              <Modal\n                open={showCreateDialog}\n                onOpenChange={(open) => {\n                  setShowCreateDialog(open);\n                  if (!open) resetCreateForm();\n                }}\n                className=\"w-[95vw] max-w-6xl\"\n              >\n                <ModalContent\n                  className=\"w-full max-h-[95vh] overflow-hidden flex flex-col\"\n                  showCloseButton={true}\n                  onClose={() => {\n                    setShowCreateDialog(false);\n                    resetCreateForm();\n                  }}\n                >\n                  <ModalHeader>\n                    <ModalTitle className=\"flex items-center gap-2\">\n                      {isCreatingKey && <Loader2 className=\"h-4 w-4 animate-spin\" />}\n                      Create New API Key\n                    </ModalTitle>\n                    <ModalDescription>\n                      Generate a new API key with specific permissions and access levels\n                    </ModalDescription>\n                  </ModalHeader>\n\n                  <div className=\"flex-1 overflow-y-auto px-8 py-6\">\n                    <div className=\"space-y-10\">\n                      <FormSection\n                        title=\"Basic Information\"\n                        description=\"Provide a name for your API key to help identify its purpose\"\n                      >\n                        <FormField\n                          label=\"API Key Name\"\n                          description=\"Choose a descriptive name like 'Production API' or 'Mobile App'\"\n                          required\n                        >\n                          <Input\n                            placeholder=\"e.g., Production API, Mobile App\"\n                            value={newKeyName}\n                            onChange={(e) => setNewKeyName(e.target.value)}\n                          />\n                        </FormField>\n                      </FormSection>\n\n                <FormSection\n                  title=\"API Permissions\"\n                  description=\"Configure access levels and permissions for your API key. Choose from essential permissions for core functionality or advanced permissions for specialized features.\"\n                >\n                  <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-8\">\n                    {/* Essential Permissions Section */}\n                    <div className=\"space-y-4\">\n                      <div className=\"flex items-center justify-between bg-gradient-to-r from-blue-50 to-blue-100 p-4 rounded-xl border border-blue-200\">\n                        <div className=\"flex items-center gap-3\">\n                          <div className=\"p-2 bg-blue-500 rounded-lg\">\n                            <Shield className=\"h-5 w-5 text-white\" />\n                          </div>\n                          <div>\n                            <h4 className=\"font-semibold text-blue-900\">{permissionCategories.essential.title}</h4>\n                            <p className=\"text-sm text-blue-700\">Recommended for most applications</p>\n                          </div>\n                          <Tooltip>\n                            <TooltipTrigger>\n                              <Info className=\"h-4 w-4 text-blue-600 hover:text-blue-800 transition-colors\" />\n                            </TooltipTrigger>\n                            <TooltipContent>\n                              <p className=\"max-w-xs\">{permissionCategories.essential.description}</p>\n                            </TooltipContent>\n                          </Tooltip>\n                        </div>\n                        <div className=\"flex items-center space-x-2\">\n                          <input\n                            type=\"checkbox\"\n                            id=\"select-all-essential\"\n                            checked={selectAllEssential}\n                            onChange={(e) => handleSelectAllEssential(e.target.checked)}\n                            className=\"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2\"\n                          />\n                          <Label htmlFor=\"select-all-essential\" className=\"text-sm font-medium text-blue-800 cursor-pointer\">\n                            Select All\n                          </Label>\n                        </div>\n                      </div>\n\n                      <div className=\"space-y-3\">\n                        {permissionCategories.essential.permissions.map((permission) => (\n                          <div key={permission.name} className=\"group bg-white border border-blue-200 rounded-lg p-4 hover:shadow-md hover:border-blue-300 transition-all duration-200\">\n                            <div className=\"flex items-start space-x-3\">\n                              <input\n                                type=\"checkbox\"\n                                id={permission.name}\n                                checked={newKeyPermissions.includes(permission.name)}\n                                onChange={(e) => handlePermissionChange(permission.name, e.target.checked)}\n                                className=\"mt-1 w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500 focus:ring-2\"\n                              />\n                              <div className=\"flex-1 min-w-0\">\n                                <Label htmlFor={permission.name} className=\"text-sm font-semibold text-blue-900 cursor-pointer group-hover:text-blue-700 transition-colors\">\n                                  {permission.label}\n                                </Label>\n                                <p className=\"text-xs text-blue-600 mt-1 leading-relaxed\">\n                                  {permission.description}\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n\n                    {/* Advanced Permissions Section */}\n                    <div className=\"space-y-4\">\n                      <div className=\"bg-gradient-to-r from-orange-50 to-orange-100 p-4 rounded-xl border border-orange-200\">\n                        <div className=\"flex items-center gap-3\">\n                          <div className=\"p-2 bg-orange-500 rounded-lg\">\n                            <Activity className=\"h-5 w-5 text-white\" />\n                          </div>\n                          <div>\n                            <h4 className=\"font-semibold text-orange-900\">{permissionCategories.advanced.title}</h4>\n                            <p className=\"text-sm text-orange-700\">For specialized features and admin tasks</p>\n                          </div>\n                          <Tooltip>\n                            <TooltipTrigger>\n                              <Info className=\"h-4 w-4 text-orange-600 hover:text-orange-800 transition-colors\" />\n                            </TooltipTrigger>\n                            <TooltipContent>\n                              <p className=\"max-w-xs\">{permissionCategories.advanced.description}</p>\n                            </TooltipContent>\n                          </Tooltip>\n                        </div>\n                      </div>\n\n                      <div className=\"space-y-3 max-h-96 overflow-y-auto pr-2\">\n                        {permissionCategories.advanced.permissions.map((permission) => (\n                          <div key={permission.name} className=\"group bg-white border border-orange-200 rounded-lg p-4 hover:shadow-md hover:border-orange-300 transition-all duration-200\">\n                            <div className=\"flex items-start space-x-3\">\n                              <input\n                                type=\"checkbox\"\n                                id={permission.name}\n                                checked={newKeyPermissions.includes(permission.name)}\n                                onChange={(e) => handlePermissionChange(permission.name, e.target.checked)}\n                                className=\"mt-1 w-4 h-4 text-orange-600 bg-gray-100 border-gray-300 rounded focus:ring-orange-500 focus:ring-2\"\n                              />\n                              <div className=\"flex-1 min-w-0\">\n                                <Label htmlFor={permission.name} className=\"text-sm font-semibold text-orange-900 cursor-pointer group-hover:text-orange-700 transition-colors\">\n                                  {permission.label}\n                                </Label>\n                                <p className=\"text-xs text-orange-600 mt-1 leading-relaxed\">\n                                  {permission.description}\n                                </p>\n                              </div>\n                            </div>\n                          </div>\n                        ))}\n                      </div>\n                    </div>\n                  </div>\n\n                  {/* Selected Permissions Summary */}\n                  {newKeyPermissions.length > 0 && (\n                    <div className=\"mt-6 p-4 bg-gray-50 border border-gray-200 rounded-xl\">\n                      <h5 className=\"font-medium text-gray-900 mb-3\">Selected Permissions ({newKeyPermissions.length})</h5>\n                      <div className=\"flex flex-wrap gap-2\">\n                        {newKeyPermissions.map((permission) => (\n                          <div key={permission} className=\"inline-flex items-center gap-1 px-3 py-1 bg-white border border-gray-300 rounded-full text-xs font-medium text-gray-700\">\n                            <Shield className=\"h-3 w-3\" />\n                            {permission}\n                          </div>\n                        ))}\n                      </div>\n                      </div>\n                    )}\n                  </FormSection>\n                    </div>\n                  </div>\n\n                  <ModalFooter>\n                    <div className=\"flex items-center justify-between w-full\">\n                      <div className=\"text-sm text-muted-foreground\">\n                        {newKeyPermissions.length > 0 ? (\n                          <span className=\"flex items-center gap-1\">\n                            <Shield className=\"h-4 w-4\" />\n                            {newKeyPermissions.length} permission{newKeyPermissions.length !== 1 ? 's' : ''} selected\n                          </span>\n                        ) : (\n                          \"No permissions selected\"\n                        )}\n                      </div>\n                      <div className=\"flex gap-3\">\n                        <Button\n                          variant=\"outline\"\n                          onClick={() => {\n                            setShowCreateDialog(false);\n                            resetCreateForm();\n                          }}\n                          disabled={isCreatingKey}\n                          className=\"px-6\"\n                        >\n                          Cancel\n                        </Button>\n                        <Button\n                          onClick={handleCreateApiKey}\n                          disabled={!newKeyName.trim() || isCreatingKey}\n                          className=\"px-6 bg-gradient-to-r from-blue-600 to-blue-700 hover:from-blue-700 hover:to-blue-800 text-white shadow-lg hover:shadow-xl transition-all duration-200\"\n                        >\n                          {isCreatingKey ? (\n                            <>\n                              <Loader2 className=\"h-4 w-4 mr-2 animate-spin\" />\n                              Creating API Key...\n                            </>\n                          ) : (\n                            <>\n                              <Key className=\"h-4 w-4 mr-2\" />\n                              Create API Key\n                            </>\n                          )}\n                        </Button>\n                      </div>\n                    </div>\n                  </ModalFooter>\n                </ModalContent>\n              </Modal>\n            </div>\n          </div>\n\n          {/* Stats Cards */}\n          <div className=\"grid gap-4 md:grid-cols-2 lg:grid-cols-4\">\n            <Card>\n              <CardHeader className=\"pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Total API Keys</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold\">{apiKeys?.length || 0}</div>\n              </CardContent>\n            </Card>\n            <Card>\n              <CardHeader className=\"pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Active Keys</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold text-green-600\">\n                  {apiKeys?.filter((key: any) => key.isActive).length || 0}\n                </div>\n              </CardContent>\n            </Card>\n            <Card>\n              <CardHeader className=\"pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Total Requests</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold text-blue-600\">\n                  {apiKeys?.reduce((sum: number, key: any) => sum + (key.usageCount || 0), 0).toLocaleString() || '0'}\n                </div>\n              </CardContent>\n            </Card>\n            <Card>\n              <CardHeader className=\"pb-2\">\n                <CardTitle className=\"text-sm font-medium\">Expired Keys</CardTitle>\n              </CardHeader>\n              <CardContent>\n                <div className=\"text-2xl font-bold text-red-600\">\n                  {apiKeys?.filter((key: any) => key.expiresAt && key.expiresAt < Date.now()).length || 0}\n                </div>\n              </CardContent>\n            </Card>\n          </div>\n\n          {/* API Keys Table */}\n          <Card>\n            <CardHeader>\n              <CardTitle>API Keys</CardTitle>\n              <CardDescription>\n                Manage API keys and monitor their usage\n              </CardDescription>\n            </CardHeader>\n            <CardContent>\n              <div className=\"flex items-center gap-4 mb-6\">\n                <div className=\"relative flex-1 max-w-sm\">\n                  <Search className=\"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground\" />\n                  <Input\n                    placeholder=\"Search API keys...\"\n                    value={searchTerm}\n                    onChange={(e) => setSearchTerm(e.target.value)}\n                    className=\"pl-10\"\n                  />\n                </div>\n                <Select value={statusFilter} onValueChange={setStatusFilter}>\n                  <SelectTrigger className=\"w-[140px]\">\n                    <SelectValue placeholder=\"Filter by status\" />\n                  </SelectTrigger>\n                  <SelectContent>\n                    <SelectItem value=\"all\">All Keys</SelectItem>\n                    <SelectItem value=\"active\">Active</SelectItem>\n                    <SelectItem value=\"inactive\">Inactive</SelectItem>\n                  </SelectContent>\n                </Select>\n              </div>\n\n              <div className=\"rounded-md border\">\n                <Table>\n                  <TableHeader>\n                    <TableRow>\n                      <TableHead>Name</TableHead>\n                      <TableHead>API Key</TableHead>\n                      <TableHead>Permissions</TableHead>\n                      <TableHead>Usage</TableHead>\n                      <TableHead>Status</TableHead>\n                      <TableHead>Last Used</TableHead>\n                      <TableHead className=\"text-right\">Actions</TableHead>\n                    </TableRow>\n                  </TableHeader>\n                  <TableBody>\n                    {(apiKeys || [])\n                      .filter((key: any) =>\n                        !searchTerm ||\n                        key.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                        key.key.toLowerCase().includes(searchTerm.toLowerCase())\n                      )\n                      .filter((key: any) => statusFilter === \"all\" ||\n                        (statusFilter === \"active\" && key.isActive) ||\n                        (statusFilter === \"inactive\" && !key.isActive)\n                      )\n                      .map((apiKey: any) => (\n                      <TableRow key={apiKey.id}>\n                        <TableCell>\n                          <div className=\"flex items-center gap-2\">\n                            <Key className=\"h-4 w-4 text-muted-foreground\" />\n                            <div>\n                              <div className=\"font-medium\">{apiKey.name}</div>\n                              <div className=\"text-sm text-muted-foreground\">\n                                Created {formatDate(apiKey.createdAt)}\n                              </div>\n                            </div>\n                          </div>\n                        </TableCell>\n                        <TableCell className=\"min-w-0\">\n                          <SecureApiKeyDisplay\n                            apiKey={apiKey.key}\n                            keyId={apiKey.keyId}\n                            apiKeyDocId={apiKey.id}\n                            placeholder=\"••••••••••••••••••••••••••••••••\"\n                            allowImmediateAccess={recentlyCreatedKeys.has(apiKey.id)}\n                          />\n                        </TableCell>\n                        <TableCell>\n                          <div className=\"flex flex-wrap gap-1\">\n                            {apiKey.permissions.slice(0, 2).map((permission, index) => (\n                              <Badge key={index} variant=\"secondary\" className=\"text-xs\">\n                                {permission}\n                              </Badge>\n                            ))}\n                            {apiKey.permissions.length > 2 && (\n                              <Badge variant=\"outline\" className=\"text-xs\">\n                                +{apiKey.permissions.length - 2}\n                              </Badge>\n                            )}\n                          </div>\n                        </TableCell>\n                        <TableCell>\n                          <div className=\"text-sm\">\n                            <div className=\"font-medium\">{apiKey.usageCount.toLocaleString()}</div>\n                            <div className=\"text-muted-foreground\">requests</div>\n                          </div>\n                        </TableCell>\n                        <TableCell>\n                          <div className=\"flex items-center gap-2\">\n                            {apiKey.isActive ? (\n                              <Badge variant=\"default\" className=\"bg-green-500\">Active</Badge>\n                            ) : (\n                              <Badge variant=\"secondary\">Inactive</Badge>\n                            )}\n                            {apiKey.expiresAt && apiKey.expiresAt < Date.now() && (\n                              <Badge variant=\"destructive\" className=\"text-xs\">\n                                <AlertTriangle className=\"h-3 w-3 mr-1\" />\n                                Expired\n                              </Badge>\n                            )}\n                          </div>\n                        </TableCell>\n                        <TableCell>\n                          <div className=\"text-sm\">\n                            {apiKey.lastUsedAt ? formatDate(apiKey.lastUsedAt) : 'Never'}\n                          </div>\n                        </TableCell>\n                        <TableCell className=\"text-right\">\n                          <DropdownMenu>\n                            <DropdownMenuTrigger asChild>\n                              <Button variant=\"ghost\" className=\"h-8 w-8 p-0\">\n                                <MoreHorizontal className=\"h-4 w-4\" />\n                              </Button>\n                            </DropdownMenuTrigger>\n                            <DropdownMenuContent align=\"end\">\n                              <DropdownMenuLabel>Actions</DropdownMenuLabel>\n                              <DropdownMenuItem onClick={() => openDetailsDialog(apiKey)}>\n                                <Eye className=\"mr-2 h-4 w-4\" />\n                                View Details\n                              </DropdownMenuItem>\n                              <DropdownMenuSeparator />\n                              <DropdownMenuItem\n                                className=\"text-orange-600\"\n                                onClick={() => handleRevokeApiKey(apiKey.id)}\n                              >\n                                <Trash2 className=\"mr-2 h-4 w-4\" />\n                                Revoke Key\n                              </DropdownMenuItem>\n                              <DropdownMenuItem\n                                className=\"text-red-600\"\n                                onClick={() => handleDeleteApiKey(apiKey.id)}\n                              >\n                                <Trash2 className=\"mr-2 h-4 w-4\" />\n                                Delete Permanently\n                              </DropdownMenuItem>\n                            </DropdownMenuContent>\n                          </DropdownMenu>\n                        </TableCell>\n                      </TableRow>\n                    ))}\n                  </TableBody>\n                </Table>\n              </div>\n\n              {/* Pagination */}\n              <div className=\"flex items-center justify-between mt-4\">\n                <div className=\"text-sm text-muted-foreground\">\n                  Showing {(apiKeys || []).filter((key: any) =>\n                    (!searchTerm ||\n                     key.name.toLowerCase().includes(searchTerm.toLowerCase()) ||\n                     key.key.toLowerCase().includes(searchTerm.toLowerCase())) &&\n                    (statusFilter === \"all\" ||\n                     (statusFilter === \"active\" && key.isActive) ||\n                     (statusFilter === \"inactive\" && !key.isActive))\n                  ).length} API keys\n                </div>\n                <div className=\"flex items-center gap-2\">\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => setCurrentPage(Math.max(0, currentPage - 1))}\n                    disabled={currentPage === 0}\n                  >\n                    Previous\n                  </Button>\n                  <Button\n                    variant=\"outline\"\n                    size=\"sm\"\n                    onClick={() => setCurrentPage(currentPage + 1)}\n                    disabled={(apiKeys?.length || 0) < pageSize}\n                  >\n                    Next\n                  </Button>\n                </div>\n              </div>\n            </CardContent>\n          </Card>\n\n          {/* API Key Details Dialog */}\n          <DetailDialog\n            open={showDetailsDialog}\n            onOpenChange={setShowDetailsDialog}\n            title=\"API Key Details\"\n            subtitle={`Complete information for ${selectedApiKey?.name || 'API Key'}`}\n            size=\"3xl\"\n            actions={\n              <>\n                <Button variant=\"outline\" onClick={() => setShowDetailsDialog(false)}>\n                  Close\n                </Button>\n                <Button\n                  variant=\"outline\"\n                  className=\"text-orange-600 border-orange-600 hover:bg-orange-50\"\n                  onClick={() => {\n                    if (selectedApiKey) {\n                      handleRevokeApiKey(selectedApiKey.id);\n                    }\n                  }}\n                >\n                  <Trash2 className=\"h-4 w-4 mr-2\" />\n                  Revoke Key\n                </Button>\n                <Button\n                  variant=\"destructive\"\n                  onClick={() => {\n                    if (selectedApiKey) {\n                      handleDeleteApiKey(selectedApiKey.id);\n                    }\n                  }}\n                >\n                  <Trash2 className=\"h-4 w-4 mr-2\" />\n                  Delete Permanently\n                </Button>\n              </>\n            }\n          >\n            <div className=\"space-y-6\">\n              {/* Status Badges */}\n              <div className=\"flex items-center gap-2\">\n                {selectedApiKey?.isActive ? (\n                  <Badge variant=\"default\" className=\"bg-green-500\">Active</Badge>\n                ) : (\n                  <Badge variant=\"secondary\">Inactive</Badge>\n                )}\n                {selectedApiKey?.expiresAt && selectedApiKey?.expiresAt < Date.now() && (\n                  <Badge variant=\"destructive\">\n                    <AlertTriangle className=\"h-3 w-3 mr-1\" />\n                    Expired\n                  </Badge>\n                )}\n              </div>\n\n              {/* API Key Information and Rate Limits */}\n              <DetailSection title=\"Basic Information\" columns={2}>\n                <DetailField\n                  label=\"Name\"\n                  value={selectedApiKey?.name}\n                />\n                <DetailField\n                  label=\"Created\"\n                  value={selectedApiKey?.createdAt ? formatDate(selectedApiKey?.createdAt) : 'N/A'}\n                />\n                <DetailField\n                  label=\"Last Used\"\n                  value={selectedApiKey?.lastUsedAt ? formatDate(selectedApiKey?.lastUsedAt) : 'Never'}\n                />\n                <DetailField\n                  label=\"Usage Count\"\n                  value={selectedApiKey?.usageCount?.toLocaleString() || 0}\n                />\n                {selectedApiKey?.expiresAt && (\n                  <DetailField\n                    label=\"Expires\"\n                    value={formatDate(selectedApiKey?.expiresAt)}\n                  />\n                )}\n                <DetailField\n                  label=\"Rate Limit (Per Minute)\"\n                  value={selectedApiKey?.rateLimit?.requestsPerMinute || 'N/A'}\n                />\n                <DetailField\n                  label=\"Rate Limit (Per Hour)\"\n                  value={selectedApiKey?.rateLimit?.requestsPerHour || 'N/A'}\n                />\n                <DetailField\n                  label=\"Rate Limit (Per Day)\"\n                  value={selectedApiKey?.rateLimit?.requestsPerDay || 'N/A'}\n                />\n              </DetailSection>\n\n              {/* API Key Field */}\n              <DetailSection title=\"API Key\">\n                <SecureApiKeyField\n                  label=\"API Key\"\n                  apiKey={selectedApiKey?.key || ''}\n                  keyId={selectedApiKey?.keyId || ''}\n                  apiKeyDocId={selectedApiKey?.id}\n                  description=\"This API key provides access to your account. Keep it secure and never share it publicly.\"\n                />\n              </DetailSection>\n\n              {/* Permissions */}\n              <DetailSection title=\"Permissions\">\n                <div className=\"flex flex-wrap gap-2\">\n                  {selectedApiKey?.permissions?.map((permission: string, index: number) => (\n                    <Badge key={index} variant=\"secondary\">\n                      <Shield className=\"h-3 w-3 mr-1\" />\n                      {permission}\n                    </Badge>\n                  ))}\n                </div>\n              </DetailSection>\n\n              {/* Usage Statistics */}\n              <DetailSection title=\"Usage Statistics\" columns={3}>\n                <div className=\"p-3 bg-blue-50 border border-blue-200 rounded-lg text-center\">\n                  <div className=\"text-2xl font-bold text-blue-600\">\n                    {selectedApiKey?.usageCount?.toLocaleString() || 0}\n                  </div>\n                  <div className=\"text-xs text-blue-600\">Total Requests</div>\n                </div>\n                <div className=\"p-3 bg-green-50 border border-green-200 rounded-lg text-center\">\n                  <div className=\"text-2xl font-bold text-green-600\">\n                    {selectedApiKey?.isActive ? '100%' : '0%'}\n                  </div>\n                  <div className=\"text-xs text-green-600\">Uptime</div>\n                </div>\n                <div className=\"p-3 bg-purple-50 border border-purple-200 rounded-lg text-center\">\n                  <div className=\"text-2xl font-bold text-purple-600\">\n                    {selectedApiKey?.lastUsedAt ?\n                      Math.round((Date.now() - selectedApiKey.lastUsedAt) / (1000 * 60 * 60)) : 'N/A'\n                    }h\n                  </div>\n                  <div className=\"text-xs text-purple-600\">Since Last Use</div>\n                </div>\n              </DetailSection>\n            </div>\n          </DetailDialog>\n\n          {/* Revoke Confirmation Dialog */}\n          <ConfirmDialog\n            open={showRevokeConfirm}\n            onOpenChange={setShowRevokeConfirm}\n            title=\"Revoke API Key\"\n            description=\"Are you sure you want to revoke this API key? This will disable the key but keep it in the database for audit purposes. The key can be viewed but not used for API requests.\"\n            confirmText=\"Revoke Key\"\n            cancelText=\"Cancel\"\n            variant=\"destructive\"\n            loading={isRevoking}\n            onConfirm={confirmRevokeApiKey}\n            onCancel={() => {\n              setShowRevokeConfirm(false);\n              setApiKeyToRevoke(null);\n            }}\n          />\n\n          {/* Delete Confirmation Dialog */}\n          <ConfirmDialog\n            open={showDeleteConfirm}\n            onOpenChange={setShowDeleteConfirm}\n            title=\"Delete API Key Permanently\"\n            description=\"Are you sure you want to permanently delete this API key? This action cannot be undone and will completely remove the key from the database. All audit logs will be preserved, but the key itself will be gone forever.\"\n            confirmText=\"Delete Permanently\"\n            cancelText=\"Cancel\"\n            variant=\"destructive\"\n            loading={isDeleting}\n            onConfirm={confirmDeleteApiKey}\n            onCancel={() => {\n              setShowDeleteConfirm(false);\n              setApiKeyToDelete(null);\n            }}\n          />\n        </div>\n      </DashboardLayout>\n    </ProtectedRoute>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAQA;AAQA;AAQA;AAQA;AAQA;AACA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;AAeA;AAEA;AAAA;AACA;AACA;AACA;AACA;AAxEA;;;;;;;;;;;;;;;;;;;;;;;AAgFe,SAAS;IACtB,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAU;IACzD,MAAM,CAAC,aAAa,eAAe,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC/C,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IAC1D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzD,MAAM,CAAC,YAAY,cAAc,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7C,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACvE,MAAM,CAAC,oBAAoB,sBAAsB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC7D,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,gBAAgB,kBAAkB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IACpE,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAC3D,MAAM,CAAC,qBAAqB,uBAAuB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAe,IAAI;IAChF,MAAM,WAAW;IAEjB,MAAM,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,UAAO,AAAD;IAExB,kBAAkB;IAClB,MAAM,EAAE,MAAM,OAAO,EAAE,SAAS,cAAc,EAAE,OAAO,YAAY,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,aAAU,AAAD,EAAE;QACjF,QAAQ,cAAc;QACtB,UAAU,iBAAiB,QAAQ,YAAY,iBAAiB;QAChE,OAAO;QACP,QAAQ,cAAc;IACxB;IAEA,MAAM,EAAE,MAAM,WAAW,EAAE,SAAS,YAAY,EAAE,OAAO,UAAU,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,iBAAc,AAAD;IAErF,oBAAoB;IACpB,MAAM,EAAE,YAAY,EAAE,SAAS,aAAa,EAAE,OAAO,WAAW,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD;IACnF,MAAM,EAAE,YAAY,EAAE,SAAS,UAAU,EAAE,OAAO,WAAW,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD;IAChF,MAAM,EAAE,YAAY,EAAE,SAAS,UAAU,EAAE,OAAO,WAAW,EAAE,GAAG,CAAA,GAAA,iIAAA,CAAA,kBAAe,AAAD;IAEhF,6DAA6D;IAC7D,MAAM,uBAAuB,CAAA,GAAA,wJAAA,CAAA,cAAW,AAAD,EAAE,2HAAA,CAAA,MAAG,CAAC,MAAM,CAAC,oBAAoB;IAExE,MAAM,qBAAqB;QACzB,IAAI,CAAC,SAAS,CAAC,WAAW,IAAI,IAAI;QAElC,IAAI;YACF,MAAM,UAAU,MAAM,qBAAqB;gBAAE,OAAO,MAAM,KAAK;YAAC;YAChE,MAAM,SAAS,MAAM,aAAa;gBAChC,MAAM;gBACN,aAAa;gBACb,SAAS;YACX;YAEA,0DAA0D;YAC1D,IAAI,QAAQ,IAAI;gBACd,uBAAuB,CAAA,OAAQ,IAAI,IAAI,MAAM,GAAG,CAAC,OAAO,EAAE;gBAC1D,+CAA+C;gBAC/C,WAAW;oBACT,uBAAuB,CAAA;wBACrB,MAAM,SAAS,IAAI,IAAI;wBACvB,OAAO,MAAM,CAAC,OAAO,EAAE;wBACvB,OAAO;oBACT;gBACF,GAAG,IAAI,KAAK;YACd;YAEA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,oBAAoB;YACpB,cAAc;YACd,qBAAqB,EAAE;YACvB,sBAAsB;QACxB,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,kBAAkB;QAClB,qBAAqB;IACvB;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,SAAS,CAAC,gBAAgB;QAE/B,IAAI;YACF,+BAA+B;YAC/B,MAAM,UAAU,MAAM,qBAAqB;gBAAE,OAAO,MAAM,KAAK;YAAC;YAEhE,MAAM,aAAa;gBACjB,UAAU;gBACV,WAAW;gBACX,QAAQ;YACV;YACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,qBAAqB;YACrB,kBAAkB;YAClB,IAAI,gBAAgB,OAAO,gBAAgB;gBACzC,qBAAqB;YACvB;QACF,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC;QAChB;IACF;IAEA,MAAM,qBAAqB,OAAO;QAChC,kBAAkB;QAClB,qBAAqB;IACvB;IAEA,MAAM,sBAAsB;QAC1B,IAAI,CAAC,SAAS,CAAC,gBAAgB;QAE/B,IAAI;YACF,+BAA+B;YAC/B,MAAM,UAAU,MAAM,qBAAqB;gBAAE,OAAO,MAAM,KAAK;YAAC;YAEhE,MAAM,aAAa;gBACjB,UAAU;gBACV,WAAW;gBACX,QAAQ;YACV;YACA,wIAAA,CAAA,QAAK,CAAC,OAAO,CAAC;YACd,qBAAqB;YACrB,kBAAkB;YAClB,IAAI,gBAAgB,OAAO,gBAAgB;gBACzC,qBAAqB;YACvB;QACF,EAAE,OAAO,OAAO;YACd,wIAAA,CAAA,QAAK,CAAC,KAAK,CAAC;YACZ,QAAQ,KAAK,CAAC;QAChB;IACF;IAIA,MAAM,aAAa,CAAC;QAClB,OAAO,IAAI,KAAK,WAAW,kBAAkB,CAAC,SAAS;YACrD,MAAM;YACN,OAAO;YACP,KAAK;YACL,MAAM;YACN,QAAQ;QACV;IACF;IAEA,MAAM,oBAAoB,CAAC;QACzB,kBAAkB;QAClB,qBAAqB;IACvB;IAIA,sCAAsC;IACtC,MAAM,uBAAuB;QAC3B,WAAW;YACT,OAAO;YACP,aAAa;YACb,MAAM,sMAAA,CAAA,SAAM;YACZ,aAAa;gBACX;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;aACD;QACH;QACA,UAAU;YACR,OAAO;YACP,aAAa;YACb,MAAM,0MAAA,CAAA,WAAQ;YACd,aAAa;gBACX;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;gBACA;oBACE,MAAM;oBACN,OAAO;oBACP,aAAa;gBACf;aACD;QACH;IACF;IAEA,6CAA6C;IAC7C,MAAM,0BAA0B,IAAM,qBAAqB,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,CAAA,IAAK,EAAE,IAAI;IAEhG,MAAM,2BAA2B,CAAC;QAChC,sBAAsB;QACtB,MAAM,iBAAiB;QAEvB,IAAI,SAAS;YACX,6DAA6D;YAC7D,MAAM,iBAAiB;mBAAI,IAAI,IAAI;uBAAI;uBAAsB;iBAAe;aAAE;YAC9E,qBAAqB;QACvB,OAAO;YACL,mCAAmC;YACnC,MAAM,sBAAsB,kBAAkB,MAAM,CAAC,CAAA,IAAK,CAAC,eAAe,QAAQ,CAAC;YACnF,qBAAqB;QACvB;IACF;IAEA,MAAM,yBAAyB,CAAC,gBAAwB;QACtD,IAAI,SAAS;YACX,qBAAqB;mBAAI;gBAAmB;aAAe;QAC7D,OAAO;YACL,qBAAqB,kBAAkB,MAAM,CAAC,CAAA,IAAK,MAAM;QAC3D;QAEA,sCAAsC;QACtC,MAAM,iBAAiB;QACvB,MAAM,kBAAkB,eAAe,KAAK,CAAC,CAAA,IAC3C,WAAW,mBAAmB,IAAI,OAAO,kBAAkB,QAAQ,CAAC;QAEtE,sBAAsB;IACxB;IAEA,MAAM,kBAAkB;QACtB,cAAc;QACd,qBAAqB,EAAE;QACvB,sBAAsB;IACxB;IAEA,qBACE,8OAAC,gJAAA,CAAA,iBAAc;QAAC,oBAAmB;kBACjC,cAAA,8OAAC,mJAAA,CAAA,kBAAe;sBACd,cAAA,8OAAC;gBAAI,WAAU;;kCAEb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAAoC;;;;;;kDAClD,8OAAC;wCAAE,WAAU;kDAAwB;;;;;;;;;;;;0CAIvC,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;;0DAC7B,8OAAC,0MAAA,CAAA,WAAQ;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGvC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,SAAQ;wCAAU,MAAK;;0DAC7B,8OAAC,gNAAA,CAAA,YAAS;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAGxC,8OAAC,kIAAA,CAAA,SAAM;wCAAC,MAAK;wCAAK,SAAS,IAAM,oBAAoB;;0DACnD,8OAAC,kMAAA,CAAA,OAAI;gDAAC,WAAU;;;;;;4CAAiB;;;;;;;kDAInC,8OAAC,iIAAA,CAAA,QAAK;wCACJ,MAAM;wCACN,cAAc,CAAC;4CACb,oBAAoB;4CACpB,IAAI,CAAC,MAAM;wCACb;wCACA,WAAU;kDAEV,cAAA,8OAAC,iIAAA,CAAA,eAAY;4CACX,WAAU;4CACV,iBAAiB;4CACjB,SAAS;gDACP,oBAAoB;gDACpB;4CACF;;8DAEA,8OAAC,iIAAA,CAAA,cAAW;;sEACV,8OAAC,iIAAA,CAAA,aAAU;4DAAC,WAAU;;gEACnB,+BAAiB,8OAAC,iNAAA,CAAA,UAAO;oEAAC,WAAU;;;;;;gEAA0B;;;;;;;sEAGjE,8OAAC,iIAAA,CAAA,mBAAgB;sEAAC;;;;;;;;;;;;8DAKpB,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC,8IAAA,CAAA,cAAW;gEACV,OAAM;gEACN,aAAY;0EAEZ,cAAA,8OAAC,8IAAA,CAAA,YAAS;oEACR,OAAM;oEACN,aAAY;oEACZ,QAAQ;8EAER,cAAA,8OAAC,iIAAA,CAAA,QAAK;wEACJ,aAAY;wEACZ,OAAO;wEACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;;;;;;;;;;;;;;;;0EAKzD,8OAAC,8IAAA,CAAA,cAAW;gEACV,OAAM;gEACN,aAAY;;kFAEZ,8OAAC;wEAAI,WAAU;;0FAEb,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAI,WAAU;;0GACb,8OAAC;gGAAI,WAAU;;kHACb,8OAAC;wGAAI,WAAU;kHACb,cAAA,8OAAC,sMAAA,CAAA,SAAM;4GAAC,WAAU;;;;;;;;;;;kHAEpB,8OAAC;;0HACC,8OAAC;gHAAG,WAAU;0HAA+B,qBAAqB,SAAS,CAAC,KAAK;;;;;;0HACjF,8OAAC;gHAAE,WAAU;0HAAwB;;;;;;;;;;;;kHAEvC,8OAAC,mIAAA,CAAA,UAAO;;0HACN,8OAAC,mIAAA,CAAA,iBAAc;0HACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;oHAAC,WAAU;;;;;;;;;;;0HAElB,8OAAC,mIAAA,CAAA,iBAAc;0HACb,cAAA,8OAAC;oHAAE,WAAU;8HAAY,qBAAqB,SAAS,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;0GAIzE,8OAAC;gGAAI,WAAU;;kHACb,8OAAC;wGACC,MAAK;wGACL,IAAG;wGACH,SAAS;wGACT,UAAU,CAAC,IAAM,yBAAyB,EAAE,MAAM,CAAC,OAAO;wGAC1D,WAAU;;;;;;kHAEZ,8OAAC,iIAAA,CAAA,QAAK;wGAAC,SAAQ;wGAAuB,WAAU;kHAAmD;;;;;;;;;;;;;;;;;;kGAMvG,8OAAC;wFAAI,WAAU;kGACZ,qBAAqB,SAAS,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,2BAC/C,8OAAC;gGAA0B,WAAU;0GACnC,cAAA,8OAAC;oGAAI,WAAU;;sHACb,8OAAC;4GACC,MAAK;4GACL,IAAI,WAAW,IAAI;4GACnB,SAAS,kBAAkB,QAAQ,CAAC,WAAW,IAAI;4GACnD,UAAU,CAAC,IAAM,uBAAuB,WAAW,IAAI,EAAE,EAAE,MAAM,CAAC,OAAO;4GACzE,WAAU;;;;;;sHAEZ,8OAAC;4GAAI,WAAU;;8HACb,8OAAC,iIAAA,CAAA,QAAK;oHAAC,SAAS,WAAW,IAAI;oHAAE,WAAU;8HACxC,WAAW,KAAK;;;;;;8HAEnB,8OAAC;oHAAE,WAAU;8HACV,WAAW,WAAW;;;;;;;;;;;;;;;;;;+FAdrB,WAAW,IAAI;;;;;;;;;;;;;;;;0FAwB/B,8OAAC;gFAAI,WAAU;;kGACb,8OAAC;wFAAI,WAAU;kGACb,cAAA,8OAAC;4FAAI,WAAU;;8GACb,8OAAC;oGAAI,WAAU;8GACb,cAAA,8OAAC,0MAAA,CAAA,WAAQ;wGAAC,WAAU;;;;;;;;;;;8GAEtB,8OAAC;;sHACC,8OAAC;4GAAG,WAAU;sHAAiC,qBAAqB,QAAQ,CAAC,KAAK;;;;;;sHAClF,8OAAC;4GAAE,WAAU;sHAA0B;;;;;;;;;;;;8GAEzC,8OAAC,mIAAA,CAAA,UAAO;;sHACN,8OAAC,mIAAA,CAAA,iBAAc;sHACb,cAAA,8OAAC,kMAAA,CAAA,OAAI;gHAAC,WAAU;;;;;;;;;;;sHAElB,8OAAC,mIAAA,CAAA,iBAAc;sHACb,cAAA,8OAAC;gHAAE,WAAU;0HAAY,qBAAqB,QAAQ,CAAC,WAAW;;;;;;;;;;;;;;;;;;;;;;;;;;;;kGAM1E,8OAAC;wFAAI,WAAU;kGACZ,qBAAqB,QAAQ,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,2BAC9C,8OAAC;gGAA0B,WAAU;0GACnC,cAAA,8OAAC;oGAAI,WAAU;;sHACb,8OAAC;4GACC,MAAK;4GACL,IAAI,WAAW,IAAI;4GACnB,SAAS,kBAAkB,QAAQ,CAAC,WAAW,IAAI;4GACnD,UAAU,CAAC,IAAM,uBAAuB,WAAW,IAAI,EAAE,EAAE,MAAM,CAAC,OAAO;4GACzE,WAAU;;;;;;sHAEZ,8OAAC;4GAAI,WAAU;;8HACb,8OAAC,iIAAA,CAAA,QAAK;oHAAC,SAAS,WAAW,IAAI;oHAAE,WAAU;8HACxC,WAAW,KAAK;;;;;;8HAEnB,8OAAC;oHAAE,WAAU;8HACV,WAAW,WAAW;;;;;;;;;;;;;;;;;;+FAdrB,WAAW,IAAI;;;;;;;;;;;;;;;;;;;;;;oEAyBhC,kBAAkB,MAAM,GAAG,mBAC1B,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAG,WAAU;;oFAAiC;oFAAuB,kBAAkB,MAAM;oFAAC;;;;;;;0FAC/F,8OAAC;gFAAI,WAAU;0FACZ,kBAAkB,GAAG,CAAC,CAAC,2BACtB,8OAAC;wFAAqB,WAAU;;0GAC9B,8OAAC,sMAAA,CAAA,SAAM;gGAAC,WAAU;;;;;;4FACjB;;uFAFO;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;8DAYlB,8OAAC,iIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC;wDAAI,WAAU;;0EACb,8OAAC;gEAAI,WAAU;0EACZ,kBAAkB,MAAM,GAAG,kBAC1B,8OAAC;oEAAK,WAAU;;sFACd,8OAAC,sMAAA,CAAA,SAAM;4EAAC,WAAU;;;;;;wEACjB,kBAAkB,MAAM;wEAAC;wEAAY,kBAAkB,MAAM,KAAK,IAAI,MAAM;wEAAG;;;;;;2EAGlF;;;;;;0EAGJ,8OAAC;gEAAI,WAAU;;kFACb,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAQ;wEACR,SAAS;4EACP,oBAAoB;4EACpB;wEACF;wEACA,UAAU;wEACV,WAAU;kFACX;;;;;;kFAGD,8OAAC,kIAAA,CAAA,SAAM;wEACL,SAAS;wEACT,UAAU,CAAC,WAAW,IAAI,MAAM;wEAChC,WAAU;kFAET,8BACC;;8FACE,8OAAC,iNAAA,CAAA,UAAO;oFAAC,WAAU;;;;;;gFAA8B;;yGAInD;;8FACE,8OAAC,gMAAA,CAAA,MAAG;oFAAC,WAAU;;;;;;gFAAiB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAcpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;;;;;;kDAE7C,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDAAsB,SAAS,UAAU;;;;;;;;;;;;;;;;;0CAG5D,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;;;;;;kDAE7C,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDACZ,SAAS,OAAO,CAAC,MAAa,IAAI,QAAQ,EAAE,UAAU;;;;;;;;;;;;;;;;;0CAI7D,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;;;;;;kDAE7C,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDACZ,SAAS,OAAO,CAAC,KAAa,MAAa,MAAM,CAAC,IAAI,UAAU,IAAI,CAAC,GAAG,GAAG,oBAAoB;;;;;;;;;;;;;;;;;0CAItG,8OAAC,gIAAA,CAAA,OAAI;;kDACH,8OAAC,gIAAA,CAAA,aAAU;wCAAC,WAAU;kDACpB,cAAA,8OAAC,gIAAA,CAAA,YAAS;4CAAC,WAAU;sDAAsB;;;;;;;;;;;kDAE7C,8OAAC,gIAAA,CAAA,cAAW;kDACV,cAAA,8OAAC;4CAAI,WAAU;sDACZ,SAAS,OAAO,CAAC,MAAa,IAAI,SAAS,IAAI,IAAI,SAAS,GAAG,KAAK,GAAG,IAAI,UAAU;;;;;;;;;;;;;;;;;;;;;;;kCAO9F,8OAAC,gIAAA,CAAA,OAAI;;0CACH,8OAAC,gIAAA,CAAA,aAAU;;kDACT,8OAAC,gIAAA,CAAA,YAAS;kDAAC;;;;;;kDACX,8OAAC,gIAAA,CAAA,kBAAe;kDAAC;;;;;;;;;;;;0CAInB,8OAAC,gIAAA,CAAA,cAAW;;kDACV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;kEAClB,8OAAC,iIAAA,CAAA,QAAK;wDACJ,aAAY;wDACZ,OAAO;wDACP,UAAU,CAAC,IAAM,cAAc,EAAE,MAAM,CAAC,KAAK;wDAC7C,WAAU;;;;;;;;;;;;0DAGd,8OAAC,kIAAA,CAAA,SAAM;gDAAC,OAAO;gDAAc,eAAe;;kEAC1C,8OAAC,kIAAA,CAAA,gBAAa;wDAAC,WAAU;kEACvB,cAAA,8OAAC,kIAAA,CAAA,cAAW;4DAAC,aAAY;;;;;;;;;;;kEAE3B,8OAAC,kIAAA,CAAA,gBAAa;;0EACZ,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAM;;;;;;0EACxB,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAS;;;;;;0EAC3B,8OAAC,kIAAA,CAAA,aAAU;gEAAC,OAAM;0EAAW;;;;;;;;;;;;;;;;;;;;;;;;kDAKnC,8OAAC;wCAAI,WAAU;kDACb,cAAA,8OAAC,iIAAA,CAAA,QAAK;;8DACJ,8OAAC,iIAAA,CAAA,cAAW;8DACV,cAAA,8OAAC,iIAAA,CAAA,WAAQ;;0EACP,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;0EAAC;;;;;;0EACX,8OAAC,iIAAA,CAAA,YAAS;gEAAC,WAAU;0EAAa;;;;;;;;;;;;;;;;;8DAGtC,8OAAC,iIAAA,CAAA,YAAS;8DACP,CAAC,WAAW,EAAE,EACZ,MAAM,CAAC,CAAC,MACP,CAAC,cACD,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACtD,IAAI,GAAG,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,KAEtD,MAAM,CAAC,CAAC,MAAa,iBAAiB,SACpC,iBAAiB,YAAY,IAAI,QAAQ,IACzC,iBAAiB,cAAc,CAAC,IAAI,QAAQ,EAE9C,GAAG,CAAC,CAAC,uBACN,8OAAC,iIAAA,CAAA,WAAQ;;8EACP,8OAAC,iIAAA,CAAA,YAAS;8EACR,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC,gMAAA,CAAA,MAAG;gFAAC,WAAU;;;;;;0FACf,8OAAC;;kGACC,8OAAC;wFAAI,WAAU;kGAAe,OAAO,IAAI;;;;;;kGACzC,8OAAC;wFAAI,WAAU;;4FAAgC;4FACpC,WAAW,OAAO,SAAS;;;;;;;;;;;;;;;;;;;;;;;;8EAK5C,8OAAC,iIAAA,CAAA,YAAS;oEAAC,WAAU;8EACnB,cAAA,8OAAC,2JAAA,CAAA,sBAAmB;wEAClB,QAAQ,OAAO,GAAG;wEAClB,OAAO,OAAO,KAAK;wEACnB,aAAa,OAAO,EAAE;wEACtB,aAAY;wEACZ,sBAAsB,oBAAoB,GAAG,CAAC,OAAO,EAAE;;;;;;;;;;;8EAG3D,8OAAC,iIAAA,CAAA,YAAS;8EACR,cAAA,8OAAC;wEAAI,WAAU;;4EACZ,OAAO,WAAW,CAAC,KAAK,CAAC,GAAG,GAAG,GAAG,CAAC,CAAC,YAAY,sBAC/C,8OAAC,iIAAA,CAAA,QAAK;oFAAa,SAAQ;oFAAY,WAAU;8FAC9C;mFADS;;;;;4EAIb,OAAO,WAAW,CAAC,MAAM,GAAG,mBAC3B,8OAAC,iIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAU,WAAU;;oFAAU;oFACzC,OAAO,WAAW,CAAC,MAAM,GAAG;;;;;;;;;;;;;;;;;;8EAKtC,8OAAC,iIAAA,CAAA,YAAS;8EACR,cAAA,8OAAC;wEAAI,WAAU;;0FACb,8OAAC;gFAAI,WAAU;0FAAe,OAAO,UAAU,CAAC,cAAc;;;;;;0FAC9D,8OAAC;gFAAI,WAAU;0FAAwB;;;;;;;;;;;;;;;;;8EAG3C,8OAAC,iIAAA,CAAA,YAAS;8EACR,cAAA,8OAAC;wEAAI,WAAU;;4EACZ,OAAO,QAAQ,iBACd,8OAAC,iIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAU,WAAU;0FAAe;;;;;qGAElD,8OAAC,iIAAA,CAAA,QAAK;gFAAC,SAAQ;0FAAY;;;;;;4EAE5B,OAAO,SAAS,IAAI,OAAO,SAAS,GAAG,KAAK,GAAG,oBAC9C,8OAAC,iIAAA,CAAA,QAAK;gFAAC,SAAQ;gFAAc,WAAU;;kGACrC,8OAAC,wNAAA,CAAA,gBAAa;wFAAC,WAAU;;;;;;oFAAiB;;;;;;;;;;;;;;;;;;8EAMlD,8OAAC,iIAAA,CAAA,YAAS;8EACR,cAAA,8OAAC;wEAAI,WAAU;kFACZ,OAAO,UAAU,GAAG,WAAW,OAAO,UAAU,IAAI;;;;;;;;;;;8EAGzD,8OAAC,iIAAA,CAAA,YAAS;oEAAC,WAAU;8EACnB,cAAA,8OAAC,4IAAA,CAAA,eAAY;;0FACX,8OAAC,4IAAA,CAAA,sBAAmB;gFAAC,OAAO;0FAC1B,cAAA,8OAAC,kIAAA,CAAA,SAAM;oFAAC,SAAQ;oFAAQ,WAAU;8FAChC,cAAA,8OAAC,gNAAA,CAAA,iBAAc;wFAAC,WAAU;;;;;;;;;;;;;;;;0FAG9B,8OAAC,4IAAA,CAAA,sBAAmB;gFAAC,OAAM;;kGACzB,8OAAC,4IAAA,CAAA,oBAAiB;kGAAC;;;;;;kGACnB,8OAAC,4IAAA,CAAA,mBAAgB;wFAAC,SAAS,IAAM,kBAAkB;;0GACjD,8OAAC,gMAAA,CAAA,MAAG;gGAAC,WAAU;;;;;;4FAAiB;;;;;;;kGAGlC,8OAAC,4IAAA,CAAA,wBAAqB;;;;;kGACtB,8OAAC,4IAAA,CAAA,mBAAgB;wFACf,WAAU;wFACV,SAAS,IAAM,mBAAmB,OAAO,EAAE;;0GAE3C,8OAAC,0MAAA,CAAA,SAAM;gGAAC,WAAU;;;;;;4FAAiB;;;;;;;kGAGrC,8OAAC,4IAAA,CAAA,mBAAgB;wFACf,WAAU;wFACV,SAAS,IAAM,mBAAmB,OAAO,EAAE;;0GAE3C,8OAAC,0MAAA,CAAA,SAAM;gGAAC,WAAU;;;;;;4FAAiB;;;;;;;;;;;;;;;;;;;;;;;;;2DAtF9B,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;kDAmGhC,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAI,WAAU;;oDAAgC;oDACpC,CAAC,WAAW,EAAE,EAAE,MAAM,CAAC,CAAC,MAC/B,CAAC,CAAC,cACD,IAAI,IAAI,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,OACtD,IAAI,GAAG,CAAC,WAAW,GAAG,QAAQ,CAAC,WAAW,WAAW,GAAG,KACzD,CAAC,iBAAiB,SAChB,iBAAiB,YAAY,IAAI,QAAQ,IACzC,iBAAiB,cAAc,CAAC,IAAI,QAAQ,AAAC,GAC/C,MAAM;oDAAC;;;;;;;0DAEX,8OAAC;gDAAI,WAAU;;kEACb,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,eAAe,KAAK,GAAG,CAAC,GAAG,cAAc;wDACxD,UAAU,gBAAgB;kEAC3B;;;;;;kEAGD,8OAAC,kIAAA,CAAA,SAAM;wDACL,SAAQ;wDACR,MAAK;wDACL,SAAS,IAAM,eAAe,cAAc;wDAC5C,UAAU,CAAC,SAAS,UAAU,CAAC,IAAI;kEACpC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAST,8OAAC,8IAAA,CAAA,eAAY;wBACX,MAAM;wBACN,cAAc;wBACd,OAAM;wBACN,UAAU,CAAC,yBAAyB,EAAE,gBAAgB,QAAQ,WAAW;wBACzE,MAAK;wBACL,uBACE;;8CACE,8OAAC,kIAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,SAAS,IAAM,qBAAqB;8CAAQ;;;;;;8CAGtE,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,WAAU;oCACV,SAAS;wCACP,IAAI,gBAAgB;4CAClB,mBAAmB,eAAe,EAAE;wCACtC;oCACF;;sDAEA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;8CAGrC,8OAAC,kIAAA,CAAA,SAAM;oCACL,SAAQ;oCACR,SAAS;wCACP,IAAI,gBAAgB;4CAClB,mBAAmB,eAAe,EAAE;wCACtC;oCACF;;sDAEA,8OAAC,0MAAA,CAAA,SAAM;4CAAC,WAAU;;;;;;wCAAiB;;;;;;;;;kCAMzC,cAAA,8OAAC;4BAAI,WAAU;;8CAEb,8OAAC;oCAAI,WAAU;;wCACZ,gBAAgB,yBACf,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;4CAAU,WAAU;sDAAe;;;;;iEAElD,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;sDAAY;;;;;;wCAE5B,gBAAgB,aAAa,gBAAgB,YAAY,KAAK,GAAG,oBAChE,8OAAC,iIAAA,CAAA,QAAK;4CAAC,SAAQ;;8DACb,8OAAC,wNAAA,CAAA,gBAAa;oDAAC,WAAU;;;;;;gDAAiB;;;;;;;;;;;;;8CAOhD,8OAAC,8IAAA,CAAA,gBAAa;oCAAC,OAAM;oCAAoB,SAAS;;sDAChD,8OAAC,8IAAA,CAAA,cAAW;4CACV,OAAM;4CACN,OAAO,gBAAgB;;;;;;sDAEzB,8OAAC,8IAAA,CAAA,cAAW;4CACV,OAAM;4CACN,OAAO,gBAAgB,YAAY,WAAW,gBAAgB,aAAa;;;;;;sDAE7E,8OAAC,8IAAA,CAAA,cAAW;4CACV,OAAM;4CACN,OAAO,gBAAgB,aAAa,WAAW,gBAAgB,cAAc;;;;;;sDAE/E,8OAAC,8IAAA,CAAA,cAAW;4CACV,OAAM;4CACN,OAAO,gBAAgB,YAAY,oBAAoB;;;;;;wCAExD,gBAAgB,2BACf,8OAAC,8IAAA,CAAA,cAAW;4CACV,OAAM;4CACN,OAAO,WAAW,gBAAgB;;;;;;sDAGtC,8OAAC,8IAAA,CAAA,cAAW;4CACV,OAAM;4CACN,OAAO,gBAAgB,WAAW,qBAAqB;;;;;;sDAEzD,8OAAC,8IAAA,CAAA,cAAW;4CACV,OAAM;4CACN,OAAO,gBAAgB,WAAW,mBAAmB;;;;;;sDAEvD,8OAAC,8IAAA,CAAA,cAAW;4CACV,OAAM;4CACN,OAAO,gBAAgB,WAAW,kBAAkB;;;;;;;;;;;;8CAKxD,8OAAC,8IAAA,CAAA,gBAAa;oCAAC,OAAM;8CACnB,cAAA,8OAAC,2JAAA,CAAA,oBAAiB;wCAChB,OAAM;wCACN,QAAQ,gBAAgB,OAAO;wCAC/B,OAAO,gBAAgB,SAAS;wCAChC,aAAa,gBAAgB;wCAC7B,aAAY;;;;;;;;;;;8CAKhB,8OAAC,8IAAA,CAAA,gBAAa;oCAAC,OAAM;8CACnB,cAAA,8OAAC;wCAAI,WAAU;kDACZ,gBAAgB,aAAa,IAAI,CAAC,YAAoB,sBACrD,8OAAC,iIAAA,CAAA,QAAK;gDAAa,SAAQ;;kEACzB,8OAAC,sMAAA,CAAA,SAAM;wDAAC,WAAU;;;;;;oDACjB;;+CAFS;;;;;;;;;;;;;;;8CASlB,8OAAC,8IAAA,CAAA,gBAAa;oCAAC,OAAM;oCAAmB,SAAS;;sDAC/C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,gBAAgB,YAAY,oBAAoB;;;;;;8DAEnD,8OAAC;oDAAI,WAAU;8DAAwB;;;;;;;;;;;;sDAEzC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACZ,gBAAgB,WAAW,SAAS;;;;;;8DAEvC,8OAAC;oDAAI,WAAU;8DAAyB;;;;;;;;;;;;sDAE1C,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;;wDACZ,gBAAgB,aACf,KAAK,KAAK,CAAC,CAAC,KAAK,GAAG,KAAK,eAAe,UAAU,IAAI,CAAC,OAAO,KAAK,EAAE,KAAK;wDAC3E;;;;;;;8DAEH,8OAAC;oDAAI,WAAU;8DAA0B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAOjD,8OAAC,8IAAA,CAAA,gBAAa;wBACZ,MAAM;wBACN,cAAc;wBACd,OAAM;wBACN,aAAY;wBACZ,aAAY;wBACZ,YAAW;wBACX,SAAQ;wBACR,SAAS;wBACT,WAAW;wBACX,UAAU;4BACR,qBAAqB;4BACrB,kBAAkB;wBACpB;;;;;;kCAIF,8OAAC,8IAAA,CAAA,gBAAa;wBACZ,MAAM;wBACN,cAAc;wBACd,OAAM;wBACN,aAAY;wBACZ,aAAY;wBACZ,YAAW;wBACX,SAAQ;wBACR,SAAS;wBACT,WAAW;wBACX,UAAU;4BACR,qBAAqB;4BACrB,kBAAkB;wBACpB;;;;;;;;;;;;;;;;;;;;;;AAMZ", "debugId": null}}]}