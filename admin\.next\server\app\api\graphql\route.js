const CHUNK_PUBLIC_PATH = "server/app/api/graphql/route.js";
const runtime = require("../../../chunks/[turbopack]_runtime.js");
runtime.loadChunk("server/chunks/node_modules_c20db4d8._.js");
runtime.loadChunk("server/chunks/node_modules_next_dist_70e42d6e._.js");
runtime.loadChunk("server/chunks/node_modules_graphql_b585dad3._.js");
runtime.loadChunk("server/chunks/node_modules_1d71c5dc._.js");
runtime.loadChunk("server/chunks/node_modules_292cdb59._.js");
runtime.loadChunk("server/chunks/node_modules_@apollo_server_dist_esm_f34ef4b7._.js");
runtime.loadChunk("server/chunks/node_modules_convex_dist_esm_2b13e49b._.js");
runtime.loadChunk("server/chunks/node_modules_0e6e0242._.js");
runtime.loadChunk("server/chunks/[root-of-the-server]__195dda5a._.js");
runtime.getOrInstantiateRuntimeModule("[project]/.next-internal/server/app/api/graphql/route/actions.js [app-rsc] (server actions loader, ecmascript)", CHUNK_PUBLIC_PATH);
runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/graphql/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH);
module.exports = runtime.getOrInstantiateRuntimeModule("[project]/node_modules/next/dist/esm/build/templates/app-route.js { INNER_APP_ROUTE => \"[project]/src/app/api/graphql/route.ts [app-route] (ecmascript)\" } [app-route] (ecmascript)", CHUNK_PUBLIC_PATH).exports;
